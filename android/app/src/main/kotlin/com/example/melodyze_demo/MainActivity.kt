package com.example.melodyze_demo

import android.media.MediaScannerConnection
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "media_scan_channel")
                .setMethodCallHandler { call, result ->
                    if (call.method == "scanFile") {
                        val path = call.argument<String>("path")
                        if (path != null) {
                            MediaScannerConnection.scanFile(context, arrayOf(path), null, null)
                            result.success(true)
                        } else {
                            result.error("INVALID_PATH", "Path was null", null)
                        }
                    }
                }
    }
}
