    import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
    import java.util.Locale

    allprojects {
        repositories {
            google()
            mavenCentral()
            maven { url = uri("https://www.jitpack.io") }
            maven { url = uri("https://www.arthenica.com/maven") }
        }
    }

   subprojects {
    afterEvaluate {
        // Apply namespace fix (for legacy plugins)
        if (plugins.hasPlugin("com.android.library")) {
            extensions.findByName("android")?.let { ext ->
                val androidExtension = ext as com.android.build.gradle.LibraryExtension
                if (androidExtension.namespace == null) {
                    androidExtension.namespace = project.group.toString()
                }
            }
        }

        tasks.withType<JavaCompile>().configureEach {
            sourceCompatibility = "17"
            targetCompatibility = "17"
        }

        tasks.withType<KotlinCompile>().configureEach {
            kotlinOptions.jvmTarget = "17"
        }
    }
}


    // Optional custom build directory (you can skip this if not needed)
    val newBuildDir = rootProject.layout.buildDirectory.dir("../../build").get()
    rootProject.layout.buildDirectory.value(newBuildDir)

    subprojects {
        val newSubprojectBuildDir = newBuildDir.dir(project.name)
        project.layout.buildDirectory.value(newSubprojectBuildDir)
        project.evaluationDependsOn(":app")
    }

    // Clean task
    tasks.register<Delete>("clean") {
        delete(rootProject.layout.buildDirectory)
    }
