# Ethnocentric Font Installation Instructions

To use the Ethnocentric font in your lyric videos, follow these steps:

## Download the Font
1. Go to Google Fonts or a font provider like DaFont
2. Search for "Ethnocentric" font
3. Download the font files (.ttf format)
4. You need two files:
   - ethnocentric.ttf (regular weight)
   - ethnocentric_bold.ttf (bold weight, or you can use the same file)

## Install in Flutter Project
1. Copy the font files to: `assets/fonts/`
2. Rename them to:
   - `ethnocentric.ttf`
   - `ethnocentric_bold.ttf`

## Alternative Free Fonts (if Ethnocentric is not available)
If you can't find Ethnocentric, here are similar sci-fi/futuristic fonts:
- Orbitron
- Exo 2
- Rajdhani  
- Saira Condensed
- Electrolize

## Quick Setup
The pubspec.yaml is already configured. Just add the font files to `assets/fonts/` folder.

## Test
After adding the font files, run:
```
flutter clean
flutter pub get
```

Then generate a video to see the Ethnocentric font in action!
