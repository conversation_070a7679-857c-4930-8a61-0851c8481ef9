class LyricLine {
  final double time;
  final String text;

  LyricLine({required this.time, required this.text});

  factory LyricLine.fromJson(Map<String, dynamic> json) {
    return LyricLine(
      time: _parseTimeToSeconds(json['start_time'] ?? '00:00:000'),
      text: json['text'] ?? '',
    );
  }

  // Convert time format "00:10:780" to seconds
  static double _parseTimeToSeconds(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length == 3) {
        final minutes = int.parse(parts[0]);
        final seconds = int.parse(parts[1]);
        final milliseconds = int.parse(parts[2]);
        return (minutes * 60) + seconds + (milliseconds / 1000);
      }
    } catch (e) {
      print('Error parsing time: $timeString');
    }
    return 0.0;
  }
}

class SongData {
  final List<LyricLine> countdown;
  final List<LyricLine> lyrics;
  final String tempo;

  SongData({
    required this.countdown,
    required this.lyrics,
    required this.tempo,
  });

  factory SongData.fromJson(Map<String, dynamic> json) {
    return SongData(
      countdown: (json['countdown']?['data'] as List?)
              ?.map((item) => LyricLine.fromJson(item))
              .toList() ??
          [],
      lyrics: (json['lyrics']?['data'] as List?)
              ?.map((item) => LyricLine.fromJson(item))
              .toList() ??
          [],
      tempo: json['tempo'] ?? '90',
    );
  }

  // Get all lines in chronological order
  List<LyricLine> getAllLines() {
    final allLines = [...countdown, ...lyrics];
    allLines.sort((a, b) => a.time.compareTo(b.time));
    return allLines;
  }
}
