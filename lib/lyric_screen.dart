import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:ui' as ui;
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'lyrics_data.dart';
import 'canva_template.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:camera/camera.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import 'package:flutter/rendering.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';

class LyricScreen extends StatefulWidget {
  final File? lyricsFile;
  final File? audioFile;
  final File? imageFile;
  final String? username;

  const LyricScreen({
    Key? key,
    this.lyricsFile,
    this.audioFile,
    this.imageFile,
    this.username,
  }) : super(key: key);

  @override
  State<LyricScreen> createState() => _LyricScreenState();
}

class _LyricScreenState extends State<LyricScreen> {
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<PlayerState>? _stateSubscription;

  List<LyricLine> lyrics = [];
  SongData? songData;

  int currentLine = 0;
  late AudioPlayer player;
  final ItemScrollController scrollController = ItemScrollController();

  File? _profileImage;

  bool isPlaying = false;
  bool isLoading = true;
  bool isRecording = false;
  String? recordedVideoPath;

  String username = "username_here";
  bool isGeneratingVideo = false;
  final TextEditingController _usernameController = TextEditingController();

  // Music player state
  Duration currentPosition = Duration.zero;
  Duration totalDuration = Duration.zero;
  bool isDragging = false;

  // Canva Template System
  List<CanvaTemplate> availableTemplates = [];
  CanvaTemplate? selectedTemplate;
  int selectedTemplateIndex = 0;

  CameraController? _cameraController;
  List<CameraDescription>? cameras;

  @override
  void initState() {
    super.initState();
    player = AudioPlayer();

    print("=== LyricScreen Init Debug ===");
    print("Uploaded username: ${widget.username}");
    print("Uploaded image file: ${widget.imageFile?.path}");
    print("Uploaded lyrics file: ${widget.lyricsFile?.path}");
    print("Uploaded audio file: ${widget.audioFile?.path}");

    // Initialize everything asynchronously to avoid blocking initState
    _initializeEverything();
  }

  Future<void> _initializeEverything() async {
    try {
      print("🚨 CRITICAL DEBUG: Starting _initializeEverything()");

      // Initialize video frames for waves video
      print("🎵 Initializing waves video frames...");
      CanvaTemplateRenderer.initializeVideoFrames();
      print("🎵 Waves video initialization started");

      // Initialize camera (async)
      await _initializeCamera();
      print("🚨 CRITICAL DEBUG: Camera initialized");

      // Set username - prioritize uploaded data
      if (widget.username != null && widget.username!.isNotEmpty) {
        print("Using uploaded username: ${widget.username}");
        setState(() {
          username = widget.username!;
        });
        await _saveUsername(widget.username!);
      } else {
        print("No uploaded username, loading from preferences");
        await _loadUsername();
      }
      print("🚨 CRITICAL DEBUG: Username handling completed");

      // Set profile image - prioritize uploaded data
      if (widget.imageFile != null) {
        print("Using uploaded image: ${widget.imageFile!.path}");
        setState(() {
          _profileImage = widget.imageFile;
        });
        await _saveImagePath(widget.imageFile!.path);
      } else {
        print("No uploaded image, loading from preferences");
        await _loadImagePath();
      }
      print("🚨 CRITICAL DEBUG: Image handling completed");

      print("🚨 CRITICAL DEBUG: About to call loadLyrics()!");
      await loadLyrics();
      print("🚨 CRITICAL DEBUG: loadLyrics() call completed!");

      // Load Canva templates
      await _loadCanvaTemplates();
      print("🚨 CRITICAL DEBUG: Canva templates loaded!");

      // Set up audio listeners after everything is initialized
      _setupAudioListeners();
      print("🚨 CRITICAL DEBUG: Audio listeners set up!");
    } catch (e) {
      print("🚨 EMERGENCY ERROR in _initializeEverything(): $e");
      print("🚨 EMERGENCY STACK TRACE: ${StackTrace.current}");
      // Still try to load lyrics even if other things failed
      try {
        await loadLyrics();
        print("🚨 EMERGENCY: loadLyrics() succeeded despite earlier error");
      } catch (lyricsError) {
        print("🚨 EMERGENCY: loadLyrics() also failed: $lyricsError");
      }
    }
  }

  Future<void> _loadCanvaTemplates() async {
    try {
      print("📱 Loading Canva templates...");
      final String jsonString =
          await rootBundle.loadString('assets/canva_templates.json');
      final List<dynamic> templatesData = json.decode(jsonString);

      availableTemplates =
          templatesData.map((data) => CanvaTemplate.fromJson(data)).toList();

      if (availableTemplates.isNotEmpty) {
        selectedTemplate = availableTemplates[0]; // Default to first template
        print("✅ Loaded ${availableTemplates.length} Canva templates");
        print("🎨 Selected template: ${selectedTemplate!.name}");
      }
    } catch (e) {
      print("❌ Error loading Canva templates: $e");
      // Create fallback template if loading fails
      selectedTemplate = _createFallbackTemplate();
    }
  }

  CanvaTemplate _createFallbackTemplate() {
    return CanvaTemplate(
      templateId: 'fallback',
      name: 'Modern Music Video Template',
      layout: CanvaLayout(),
      colors: CanvaColors(),
      typography: CanvaTypography(),
      elements: [
        // Top bar - User DP with Melodyze logo (left side)
        CanvaElement(
          type: 'topbar_profile',
          position: CanvaPosition(x: 50, y: 50, width: 80, height: 80),
          style: CanvaStyle(borderColor: Colors.white, borderWidth: 3),
        ),
        // Melodyze logo collaboration text
        CanvaElement(
          type: 'melodyze_logo',
          position: CanvaPosition(x: 150, y: 75, width: 200, height: 30),
          style: CanvaStyle(color: Colors.white, fontSize: 18),
        ),
        // Song info (right side of top bar)
        CanvaElement(
          type: 'song_info',
          position: CanvaPosition(x: 600, y: 50, width: 430, height: 80),
          style: CanvaStyle(color: Colors.white, fontSize: 16),
        ),
        // Main profile picture (center, larger)
        CanvaElement(
          type: 'main_profile',
          position: CanvaPosition(x: 390, y: 200, width: 300, height: 300),
          style: CanvaStyle(borderColor: Colors.blue, borderWidth: 8),
        ),
        // Cover by text
        CanvaElement(
          type: 'title',
          position: CanvaPosition(x: 0, y: 520, width: 1080, height: 40),
          style: CanvaStyle(color: Colors.white, fontSize: 24),
          content: 'COVER BY',
        ),
        // Username
        CanvaElement(
          type: 'username_display',
          position: CanvaPosition(x: 0, y: 570, width: 1080, height: 50),
          style: CanvaStyle(color: Colors.white, fontSize: 28),
        ),
        // Lyric Video text
        CanvaElement(
          type: 'title',
          position: CanvaPosition(x: 0, y: 620, width: 1080, height: 40),
          style: CanvaStyle(color: Colors.grey, fontSize: 20),
          content: 'Lyric Video',
        ),
        // Visual wave animation (center section)
        CanvaElement(
          type: 'video_waves',
          position: CanvaPosition(x: 90, y: 720, width: 900, height: 250),
          style: CanvaStyle(color: Colors.orange, fontSize: 0),
        ),
        // Song title above lyrics
        CanvaElement(
          type: 'song_title_lyrics',
          position: CanvaPosition(x: 0, y: 1020, width: 1080, height: 60),
          style: CanvaStyle(color: Colors.white, fontSize: 36),
        ),
        // Lyrics section (3 lines with middle highlighted)
        CanvaElement(
          type: 'lyrics',
          position: CanvaPosition(x: 80, y: 1100, width: 920, height: 450),
          style: CanvaStyle(color: Colors.white, fontSize: 52),
        ),
      ],
    );
  }

  Future<void> _runFFmpegCommand(String command) async {
    try {
      print('🎬 Starting FFmpeg with command: $command');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();
      final logs = await session.getLogs();

      // Print all logs for debugging
      print('📋 FFmpeg execution logs:');
      for (final log in logs) {
        print('  ${log.getMessage()}');
      }

      if (ReturnCode.isSuccess(returnCode)) {
        print('✅ FFmpeg finished successfully');
      } else {
        final errorMessage = logs.map((log) => log.getMessage()).join('\n');
        print('❌ FFmpeg failed with return code: $returnCode');
        print('Error details: $errorMessage');
        throw Exception('FFmpeg failed with code $returnCode: $errorMessage');
      }
    } catch (e) {
      print('❌ FFmpeg execution error: $e');
      throw Exception('FFmpeg failed: $e');
    }
  }

  Future<void> loadLyrics() async {
    print("� EMERGENCY DEBUG: loadLyrics() START!");
    print("�🔄 loadLyrics() method called!");
    print("🔄 widget.lyricsFile is: ${widget.lyricsFile}");
    try {
      String jsonString;

      if (widget.lyricsFile != null) {
        print("📖 Loading lyrics from file: ${widget.lyricsFile!.path}");
        jsonString = await widget.lyricsFile!.readAsString();
      } else {
        print("📖 Loading lyrics from assets");
        jsonString = await rootBundle.loadString('assets/lyrics.json');
      }

      print("📖 Raw lyrics JSON: ${jsonString.length} characters");
      print(
          "📖 First 200 chars of JSON: ${jsonString.length > 200 ? jsonString.substring(0, 200) + '...' : jsonString}");

      // Try new format first (with "lyrics" and "countdown")
      try {
        final Map<String, dynamic> data = json.decode(jsonString);
        if (data.containsKey('lyrics') && data.containsKey('countdown')) {
          print("📖 Using new format with ${data['lyrics'].length} lyrics");
          songData = SongData.fromJson(data);
          setState(() {
            lyrics = songData!.lyrics;
            isLoading = false;
          });
          print("📖 Loaded ${lyrics.length} lyrics lines successfully");
          return;
        }
      } catch (e) {
        print("New format parsing failed, trying old format: $e");
      }

      // Fall back to old format
      final List<dynamic> data = json.decode(jsonString);
      lyrics = data.map((item) {
        // Handle both "line" and "text" properties, and direct time numbers
        final Map<String, dynamic> itemMap = item as Map<String, dynamic>;
        return LyricLine(
          time: (itemMap['time'] ?? 0.0).toDouble(),
          text: itemMap['line'] ?? itemMap['text'] ?? '',
        );
      }).toList();
      print("📖 Loaded ${lyrics.length} lyrics from old format");
      setState(() {
        isLoading = false;
      });

      // Debug: Print first few lyrics
      for (int i = 0; i < (lyrics.length > 3 ? 3 : lyrics.length); i++) {
        print("📖 Lyric $i: ${lyrics[i].time}s - '${lyrics[i].text}'");
      }
    } catch (e) {
      print("❌ Error loading lyrics: $e");
      print("❌ Stack trace: ${StackTrace.current}");

      // Provide fallback lyrics if loading fails
      lyrics = [
        LyricLine(time: 1.0, text: "Welcome to Melodyze!"),
        LyricLine(time: 4.0, text: "Your lyric video app"),
        LyricLine(time: 7.0, text: "Upload your own lyrics"),
        LyricLine(time: 10.0, text: "And create amazing videos"),
        LyricLine(time: 13.0, text: "Let's get started!"),
      ];
      print("📖 Using fallback lyrics: ${lyrics.length} lines");

      setState(() {
        isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Using default lyrics. Error: $e'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _initializeCamera() async {
    try {
      cameras = await availableCameras();
    } catch (e) {
      print("Error initializing camera: $e");
    }
  }

  Future<void> _saveUsername(String newUsername) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('username', newUsername);
  }

  Future<void> _loadUsername() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      username = prefs.getString('username') ?? 'username_here';
    });
  }

  Future<void> _saveImagePath(String imagePath) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('profile_image_path', imagePath);
  }

  Future<void> _loadImagePath() async {
    final prefs = await SharedPreferences.getInstance();
    final imagePath = prefs.getString('profile_image_path');
    if (imagePath != null && File(imagePath).existsSync()) {
      setState(() {
        _profileImage = File(imagePath);
      });
    }
  }

  void startAudio() async {
    setState(() {
      isLoading = true;
    });

    try {
      await player.stop();

      String audioPath = "";

      if (widget.audioFile != null) {
        audioPath = widget.audioFile!.path;
        print("🎵 Using uploaded audio file: $audioPath");

        if (audioPath.endsWith(".flac")) {
          // Convert FLAC to MP3 for playback
          final mp3Path = audioPath.replaceAll(".flac", ".mp3");
          print("🎵 Converting .flac to .mp3 for playback...");

          await _runFFmpegCommand('-i "$audioPath" "$mp3Path"');

          if (File(mp3Path).existsSync()) {
            audioPath = mp3Path;
            print("✅ Converted FLAC to MP3: $audioPath");
          } else {
            throw Exception("MP3 conversion failed");
          }
        }
      } else {
        // Use dummy fallback
        print("🎵 Using dummy audio file from assets");
        final byteData = await rootBundle.load('assets/dummy_audio.mp3');
        final file =
            File('${(await getTemporaryDirectory()).path}/dummy_audio.mp3');
        await file.writeAsBytes(byteData.buffer.asUint8List());
        audioPath = file.path;
        print("🎵 Dummy audio saved to: $audioPath");
      }

      print("🎵 Playing audio from: $audioPath");
      print("🎵 File exists: ${File(audioPath).existsSync()}");
      if (File(audioPath).existsSync()) {
        print("🎵 File size: ${File(audioPath).lengthSync()} bytes");
      }

      await player.play(DeviceFileSource(audioPath));

      setState(() {
        isPlaying = true;
        isLoading = false;
      });

      print("🎵 Audio started successfully");
    } catch (e) {
      print("❌ Error in startAudio(): $e");
      print("❌ Stack trace: ${StackTrace.current}");
      setState(() {
        isLoading = false;
        isPlaying = false;
      });

      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Audio playback failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _setupAudioListeners() {
    print("🎧 Setting up audio listeners...");

    // Cancel existing subscriptions
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _stateSubscription?.cancel();

    // Set up position listener
    _positionSubscription =
        player.onPositionChanged.listen((Duration position) {
      print("🎵 Position changed: ${position.inSeconds}s");
      if (!isDragging) {
        setState(() {
          currentPosition = position;
          // Update current line based on position
          for (int i = 0; i < lyrics.length; i++) {
            if (position.inSeconds >= lyrics[i].time &&
                (i == lyrics.length - 1 ||
                    position.inSeconds < lyrics[i + 1].time)) {
              if (currentLine != i) {
                print("🎶 Switching to lyric line $i: ${lyrics[i].text}");
                currentLine = i;
                scrollController.scrollTo(
                  index: i,
                  duration: const Duration(milliseconds: 300),
                  alignment: 0.5, // Center the current lyric in the view
                );
              }
              break;
            }
          }
        });
      }
    });

    // Set up duration listener
    _durationSubscription =
        player.onDurationChanged.listen((Duration duration) {
      print("🎵 Duration changed: ${duration.inSeconds}s");
      setState(() {
        totalDuration = duration;
      });
    });

    // Set up state listener
    _stateSubscription =
        player.onPlayerStateChanged.listen((PlayerState state) {
      print("🎵 Player state changed: $state");
      setState(() {
        isPlaying = state == PlayerState.playing;

        // Handle completion state
        if (state == PlayerState.completed) {
          print("🎵 Audio playback completed");
          isPlaying = false;
          // Keep the current position at the end for UI consistency
        }
      });
    });

    print("✅ Audio listeners set up successfully");
  }

  // Music player control methods
  Future<void> seekBackward() async {
    try {
      final newPosition = currentPosition - const Duration(seconds: 10);
      final seekPosition = newPosition.isNegative ? Duration.zero : newPosition;
      print("🎵 Seeking backward to: ${seekPosition.inSeconds}s");
      await player.seek(seekPosition);

      // If the player was stopped/completed, resume playback after seeking
      if (!isPlaying) {
        print("🎵 Resuming playback after backward seek...");
        await player.resume();
      }
    } catch (e) {
      print("❌ Error in seekBackward: $e");
    }
  }

  Future<void> seekForward() async {
    try {
      final newPosition = currentPosition + const Duration(seconds: 10);
      final seekPosition =
          newPosition > totalDuration ? totalDuration : newPosition;
      print("🎵 Seeking forward to: ${seekPosition.inSeconds}s");
      await player.seek(seekPosition);

      // If the player was stopped/completed and we're not at the end, resume playback
      if (!isPlaying && seekPosition < totalDuration) {
        print("🎵 Resuming playback after forward seek...");
        await player.resume();
      }
    } catch (e) {
      print("❌ Error in seekForward: $e");
    }
  }

  Future<void> seekToPosition(Duration position) async {
    try {
      print("🎵 Seeking to position: ${position.inSeconds}s");
      await player.seek(position);

      // If seeking to a position that's not at the end and player was stopped, resume
      if (!isPlaying && position < totalDuration) {
        print("🎵 Auto-resuming playback after seek...");
        await player.resume();
      }
    } catch (e) {
      print("❌ Error in seekToPosition: $e");
    }
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  Future<void> togglePlayPause() async {
    try {
      print("🎵 Toggle play/pause - current state: isPlaying=$isPlaying");
      if (isPlaying) {
        print("🎵 Pausing audio...");
        await player.pause();
      } else {
        print("🎵 Resuming audio...");
        await player.resume();
      }
    } catch (e) {
      print("❌ Error in togglePlayPause: $e");
    }
  }

  Future<void> _generateVideo() async {
    setState(() {
      isGeneratingVideo = true;
    });

    try {
      // 🎥 CRITICAL: Ensure video frames are extracted before generating video!
      print('🎥 [VIDEO GEN] Ensuring waves video frames are ready...');
      await CanvaTemplateRenderer.initializeVideoFrames();
      print(
          '🎥 [VIDEO GEN] Waves video frames ready, starting video generation...');

      await _createFFmpegVideo();
    } catch (e) {
      print("Video generation error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Video generation failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isGeneratingVideo = false;
      });
    }
  }

  Future<void> _createFFmpegVideo() async {
    final Directory tempDir = await getTemporaryDirectory();
    final String outputPath =
        '${tempDir.path}/lyric_video_${DateTime.now().millisecondsSinceEpoch}.mp4';

    print('🎬 Creating video at: $outputPath');

    try {
      // Get audio duration
      Duration audioDuration = await _getAudioDurationFromFile();
      print('🎵 Audio duration: ${audioDuration.inSeconds}s');

      if (audioDuration.inSeconds <= 0) {
        throw Exception('Invalid audio duration');
      }

      // Get audio file path
      String audioPath;
      if (widget.audioFile != null) {
        audioPath = widget.audioFile!.path;
        if (!File(audioPath).existsSync()) {
          throw Exception('Audio file does not exist: $audioPath');
        }
      } else {
        // Copy asset audio to temp directory
        final byteData = await rootBundle.load('assets/dummy_audio.mp3');
        audioPath = '${tempDir.path}/audio.mp3';
        await File(audioPath).writeAsBytes(byteData.buffer.asUint8List());
      }

      print('🎨 Creating lyric video with image frames...');

      // Generate lyric frames
      final String framesDir =
          await _generateLyricFrames(tempDir, audioDuration);

      // Create video from frames and audio
      final String ffmpegCommand =
          '-framerate 15 -i "$framesDir/frame_%03d.png" '
          '-i "$audioPath" '
          '-c:v libx264 -preset ultrafast -crf 28 '
          '-c:a aac -b:a 128k '
          '-t ${audioDuration.inSeconds} '
          '-pix_fmt yuv420p '
          '-shortest '
          '-y "$outputPath"';

      print("🎬 FFmpeg command: $ffmpegCommand");

      await _runFFmpegCommand(ffmpegCommand);

      // Check if video was created successfully
      if (File(outputPath).existsSync()) {
        final fileSize = File(outputPath).lengthSync();
        if (fileSize > 0) {
          print('✅ Video created successfully at: $outputPath');
          print(
              '📁 Video file size: ${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB');
          _showVideoSuccessDialog(outputPath);
        } else {
          throw Exception("Video file is empty (0 bytes)");
        }
      } else {
        throw Exception("Video file was not created");
      }
    } catch (e) {
      print("❌ Video generation error: $e");
      rethrow;
    }
  }

  Future<String> _generateLyricFrames(
      Directory tempDir, Duration audioDuration) async {
    final String framesDir = '${tempDir.path}/frames';
    final Directory framesDirObj = Directory(framesDir);
    if (await framesDirObj.exists()) {
      await framesDirObj.delete(recursive: true);
    }
    await framesDirObj.create();

    print('🖼️ Generating lyric frames...');

    // We'll generate frames at 2 FPS for smoother video
    final int totalFrames = (audioDuration.inSeconds * 2).round();

    for (int frameIndex = 0; frameIndex < totalFrames; frameIndex++) {
      final double currentTime = frameIndex / 15.0; // 2 FPS

      // Find which lyric should be highlighted at this time
      int currentLyricIndex = -1;
      for (int i = 0; i < lyrics.length; i++) {
        final lyric = lyrics[i];
        final startTime = lyric.time.toDouble();
        final endTime = i + 1 < lyrics.length
            ? lyrics[i + 1].time.toDouble()
            : audioDuration.inSeconds.toDouble();

        if (currentTime >= startTime && currentTime < endTime) {
          currentLyricIndex = i;
          break;
        }
      }

      // Create frame image using dynamic Canva template
      await _createCanvaTemplateFrame(
          framesDir, frameIndex, currentLyricIndex, username);

      if (frameIndex % 20 == 0) {
        print('🎨 Generated frame $frameIndex/$totalFrames');
      }
    }

    print('✅ Generated $totalFrames lyric frames');
    return framesDir;
  }

  Future<void> _createCanvaTemplateFrame(String framesDir, int frameIndex,
      int currentLyricIndex, String username) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder, const Rect.fromLTWH(0, 0, 1080, 1920));

    if (selectedTemplate != null) {
      // Extract lyrics text for template rendering
      List<String> lyricsText = lyrics.map((lyric) => lyric.text).toList();

      // Use dynamic Canva template renderer
      await CanvaTemplateRenderer.renderFrame(
        canvas: canvas,
        template: selectedTemplate!,
        username: username,
        profileImage: _profileImage,
        lyrics: lyricsText,
        currentLyricIndex: currentLyricIndex,
        frameIndex: frameIndex,
      );
    } else {
      // Fallback to old method if template fails
      await _createScrollableLyricFrameFallback(
          canvas, currentLyricIndex, username, frameIndex);
    }

    // Convert to image and save
    final picture = recorder.endRecording();
    final img = await picture.toImage(1080, 1920);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    final bytes = byteData!.buffer.asUint8List();

    final frameFile =
        File('$framesDir/frame_${frameIndex.toString().padLeft(3, '0')}.png');
    await frameFile.writeAsBytes(bytes);
  }

  Future<void> _createScrollableLyricFrameFallback(Canvas canvas,
      int currentLyricIndex, String username, int frameIndex) async {
    // Enhanced gradient background instead of plain black
    final backgroundGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF0F0F23), // Dark blue-purple
        const Color(0xFF1A1A2E), // Darker blue
        const Color(0xFF16213E), // Navy blue
        const Color(0xFF0F0F23), // Back to dark blue-purple
      ],
      stops: [0.0, 0.3, 0.7, 1.0],
    );

    final backgroundRect = const Rect.fromLTWH(0, 0, 1080, 1920);
    canvas.drawRect(
      backgroundRect,
      Paint()..shader = backgroundGradient.createShader(backgroundRect),
    );

    // Add subtle animated particles effect (optional decoration)
    final particlePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Draw some decorative dots
    for (int i = 0; i < 20; i++) {
      final x = (i * 54.0 + frameIndex * 2) % 1080;
      final y = (i * 96.0 + frameIndex * 1.5) % 1920;
      canvas.drawCircle(Offset(x, y), 2, particlePaint);
    }

    // Draw profile image with enhanced styling
    if (_profileImage != null) {
      try {
        final imageBytes = await _profileImage!.readAsBytes();
        final codec = await ui.instantiateImageCodec(imageBytes);
        final frame = await codec.getNextFrame();
        final profileImg = frame.image;

        // Enhanced profile styling - much larger and with effects
        final profileSize = 280.0; // Much larger profile
        final profileX = (1080 - profileSize) / 2;
        final profileY = 150.0; // More space from top

        // Draw outer glow effect
        final glowPaint = Paint()
          ..color = Colors.blue.withOpacity(0.3)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);
        canvas.drawCircle(
          Offset(profileX + profileSize / 2, profileY + profileSize / 2),
          profileSize / 2 + 20,
          glowPaint,
        );

        // Draw border ring with gradient effect
        final borderPaint = Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 8;
        canvas.drawCircle(
          Offset(profileX + profileSize / 2, profileY + profileSize / 2),
          profileSize / 2 + 10,
          borderPaint,
        );

        // Draw inner shadow
        final shadowPaint = Paint()
          ..color = Colors.black.withOpacity(0.3)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
        canvas.drawCircle(
          Offset(
              profileX + profileSize / 2 + 2, profileY + profileSize / 2 + 2),
          profileSize / 2,
          shadowPaint,
        );

        // Draw the actual profile image (circular)
        canvas.save();
        canvas.clipPath(Path()
          ..addOval(
              Rect.fromLTWH(profileX, profileY, profileSize, profileSize)));
        canvas.drawImageRect(
          profileImg,
          Rect.fromLTWH(
              0, 0, profileImg.width.toDouble(), profileImg.height.toDouble()),
          Rect.fromLTWH(profileX, profileY, profileSize, profileSize),
          Paint(),
        );
        canvas.restore();
      } catch (e) {
        print("Error loading profile image for video: $e");
        // Draw enhanced default circle if image fails
        final profileSize = 280.0;
        final profileX = (1080 - profileSize) / 2;
        final profileY = 150.0;

        // Glow effect
        final glowPaint = Paint()
          ..color = Colors.blue.withOpacity(0.3)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);
        canvas.drawCircle(
          Offset(profileX + profileSize / 2, profileY + profileSize / 2),
          profileSize / 2 + 20,
          glowPaint,
        );

        // Main circle
        canvas.drawCircle(
          Offset(profileX + profileSize / 2, profileY + profileSize / 2),
          profileSize / 2,
          Paint()..color = Colors.grey.shade700,
        );
      }
    } else {
      // Draw enhanced default circle
      final profileSize = 280.0;
      final profileX = (1080 - profileSize) / 2;
      final profileY = 150.0;

      // Glow effect
      final glowPaint = Paint()
        ..color = Colors.blue.withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);
      canvas.drawCircle(
        Offset(profileX + profileSize / 2, profileY + profileSize / 2),
        profileSize / 2 + 20,
        glowPaint,
      );

      // Main circle
      canvas.drawCircle(
        Offset(profileX + profileSize / 2, profileY + profileSize / 2),
        profileSize / 2,
        Paint()..color = Colors.grey.shade700,
      );
    }

    // Draw enhanced username and track info
    final profileSize = 280.0;
    final profileY = 150.0;
    final textStartY = profileY + profileSize + 40;

    // Draw "COVER BY" text (smaller, styled)
    final coverByPainter = TextPainter(
      text: TextSpan(
        text: 'COVER BY',
        style: TextStyle(
          color: Colors.white.withOpacity(0.8),
          fontSize: 24,
          fontWeight: FontWeight.w300,
          letterSpacing: 3,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.8),
              offset: Offset(2, 2),
              blurRadius: 4,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    coverByPainter.layout();
    coverByPainter.paint(
        canvas, Offset((1080 - coverByPainter.width) / 2, textStartY));

    // Draw username (large, bold, with effects)
    final usernamePainter = TextPainter(
      text: TextSpan(
        text: username.startsWith('@') ? username : '@$username',
        style: TextStyle(
          color: Colors.white,
          fontSize: 48,
          fontWeight: FontWeight.bold,
          letterSpacing: 2,
          shadows: [
            Shadow(
              color: Colors.blue.withOpacity(0.8),
              offset: Offset(0, 0),
              blurRadius: 10,
            ),
            Shadow(
              color: Colors.black.withOpacity(0.8),
              offset: Offset(3, 3),
              blurRadius: 6,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    usernamePainter.layout();
    usernamePainter.paint(
        canvas, Offset((1080 - usernamePainter.width) / 2, textStartY + 50));

    // Draw song name "Faded" (new addition)
    final songNamePainter = TextPainter(
      text: TextSpan(
        text: 'Faded',
        style: TextStyle(
          fontFamily: 'Ethnocentric',
          color: Colors.blue,
          fontSize: 50,
          fontWeight: FontWeight.bold,
          letterSpacing: 2,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.8),
              offset: Offset(3, 3),
              blurRadius: 6,
            ),
            Shadow(
              color: Colors.blue.withOpacity(0.5),
              offset: Offset(0, 0),
              blurRadius: 12,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    songNamePainter.layout();
    songNamePainter.paint(
        canvas, Offset((1080 - songNamePainter.width) / 2, textStartY + 110));

    // Draw track name if available (optional enhancement)
    final trackPainter = TextPainter(
      text: TextSpan(
        text: 'Lyric Video',
        style: TextStyle(
          color: Colors.white.withOpacity(0.7),
          fontSize: 28,
          fontWeight: FontWeight.w400,
          letterSpacing: 1,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.6),
              offset: Offset(2, 2),
              blurRadius: 4,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    trackPainter.layout();
    trackPainter.paint(
        canvas, Offset((1080 - trackPainter.width) / 2, textStartY + 180));

    // Draw ENHANCED SCROLLABLE lyrics with professional styling and dynamic spacing
    const double centerY =
        1200; // Moved further down to stay below profile section
    const double baseLineSpacing = 70; // Reduced spacing for smaller layout
    const double maxWidth = 900; // Slightly smaller width
    const int contextLines = 2; // Reduced context lines to show fewer lyrics

    // Calculate which lyrics to show (only current + context)
    int startIndex =
        (currentLyricIndex - contextLines).clamp(0, lyrics.length - 1);
    int endIndex =
        (currentLyricIndex + contextLines).clamp(0, lyrics.length - 1);

    // Pre-calculate text painters and their heights to determine proper spacing
    List<TextPainter> lyricPainters = [];
    List<double> lyricHeights = [];

    for (int i = startIndex; i <= endIndex; i++) {
      final isCurrentLine = i == currentLyricIndex;
      // Reduced font sizes significantly
      double fontSize = isCurrentLine
          ? 45 // Increased from 36 to 45 for better readability
          : (28 - (i - currentLyricIndex).abs() * 3)
              .clamp(18, 28)
              .toDouble(); // Reduced from 44 to 28
      double opacity = isCurrentLine
          ? 1.0
          : (1.0 - ((i - currentLyricIndex).abs() * 0.25)).clamp(0.3, 1.0);

      final lyricPainter = TextPainter(
        text: TextSpan(
          text: lyrics[i].text,
          style: TextStyle(
            fontFamily: 'Ethnocentric',
            color: isCurrentLine
                ? const Color(0xFFE955BB).withValues(alpha: opacity)
                : Colors.white.withValues(alpha: opacity),
            fontSize: fontSize,
            fontWeight: isCurrentLine ? FontWeight.bold : FontWeight.w500,
            letterSpacing: isCurrentLine ? 1.2 : 0.8, // Reduced letter spacing
            shadows: [
              Shadow(
                color: Colors.black.withValues(alpha: 0.9),
                offset: Offset(2, 2), // Reduced shadow offset
                blurRadius: 4, // Reduced blur
              ),
              if (isCurrentLine) ...[
                Shadow(
                  color: const Color(0xFFE955BB).withValues(alpha: 0.5),
                  offset: Offset(0, 0),
                  blurRadius: 8, // Reduced glow
                ),
              ],
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      lyricPainter.layout(maxWidth: maxWidth);

      lyricPainters.add(lyricPainter);
      lyricHeights.add(lyricPainter.height);
    }

    // Calculate positions with dynamic spacing based on text height
    double currentY = centerY;

    // Find the current line index in our painters array
    int currentPainterIndex = currentLyricIndex - startIndex;

    // Position current line at center
    if (currentPainterIndex >= 0 &&
        currentPainterIndex < lyricPainters.length) {
      currentY = centerY - (lyricHeights[currentPainterIndex] / 2);
    }

    // Draw lyrics above current line (going upward) - ensure they don't go above profile section
    double yAbove = currentY;
    for (int i = currentPainterIndex - 1; i >= 0; i--) {
      yAbove -= (lyricHeights[i] + baseLineSpacing);

      // Ensure lyrics don't go above the profile section (minimum Y = 600)
      if (yAbove + lyricHeights[i] > 600) {
        // Only draw if visible
        lyricPainters[i]
            .paint(canvas, Offset((1080 - lyricPainters[i].width) / 2, yAbove));
      }
    }

    // Draw current line
    if (currentPainterIndex >= 0 &&
        currentPainterIndex < lyricPainters.length) {
      lyricPainters[currentPainterIndex].paint(
          canvas,
          Offset(
              (1080 - lyricPainters[currentPainterIndex].width) / 2, currentY));
    }

    // Draw lyrics below current line (going downward)
    double yBelow = currentY;
    if (currentPainterIndex >= 0 && currentPainterIndex < lyricHeights.length) {
      yBelow += lyricHeights[currentPainterIndex];
    }

    for (int i = currentPainterIndex + 1; i < lyricPainters.length; i++) {
      yBelow += baseLineSpacing;

      if (yBelow < 1700) {
        // Only draw if visible
        lyricPainters[i]
            .paint(canvas, Offset((1080 - lyricPainters[i].width) / 2, yBelow));
      }

      yBelow += lyricHeights[i];
    }
  }

  Future<Duration> _getAudioDurationFromFile() async {
    final tempPlayer = AudioPlayer();

    if (widget.audioFile != null) {
      await tempPlayer.setSource(DeviceFileSource(widget.audioFile!.path));
    } else {
      final byteData = await rootBundle.load('assets/dummy_audio.mp3');
      final file =
          File('${(await getTemporaryDirectory()).path}/temp_audio.mp3');
      await file.writeAsBytes(byteData.buffer.asUint8List());
      await tempPlayer.setSource(DeviceFileSource(file.path));
    }

    final duration = await tempPlayer.getDuration();
    await tempPlayer.dispose();

    return duration ?? const Duration(seconds: 30);
  }

  void _showVideoSuccessDialog(String videoPath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Lyric Video Created!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Your lyric video for "@$username" is ready!'),
            const SizedBox(height: 10),
            Text('Saved at: $videoPath'),
            const SizedBox(height: 10),
            const Text(
                'The video includes synchronized lyrics with your audio!'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset to beginning
              setState(() {
                currentLine = 0;
              });
              player.seek(Duration.zero);
            },
            child: const Text('OK'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _shareVideo(videoPath);
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  Future<void> _shareVideo(String videoPath) async {
    try {
      await Share.shareXFiles([XFile(videoPath)],
          text: 'Check out my lyric video created with Melodyze! @$username');
    } catch (e) {
      print('Error sharing video: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing video: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    player.dispose();
    _usernameController.dispose();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _stateSubscription?.cancel();
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print(
        "🎨 Building LyricScreen - isLoading: $isLoading, lyrics.length: ${lyrics.length}");

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Lyric Video', style: TextStyle(color: Colors.white)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (isGeneratingVideo)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            )
          else
            TextButton(
              onPressed: _generateVideo,
              child: const Text(
                'SAVE',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.blue),
            )
          : Column(
              children: [
                // Profile section
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 60,
                        backgroundImage: _profileImage != null
                            ? FileImage(_profileImage!)
                            : const AssetImage('assets/dummy_dp.jpg')
                                as ImageProvider,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        username.startsWith('@') ? username : '@$username',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Faded',
                        style: const TextStyle(
                          fontFamily: 'Ethnocentric',
                          color: Colors.blue,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 2,
                        ),
                      ),
                    ],
                  ),
                ),

                // Lyrics display
                Expanded(
                  child: lyrics.isEmpty
                      ? const Center(
                          child: Text(
                            'No lyrics available',
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                      : ScrollablePositionedList.builder(
                          itemScrollController: scrollController,
                          itemCount: lyrics.length,
                          padding: const EdgeInsets.symmetric(
                              vertical: 70), // Reduced from 100 to 70
                          itemBuilder: (context, index) {
                            final isCurrentLine = index == currentLine;
                            return Container(
                              margin: const EdgeInsets.symmetric(
                                vertical: 8, // Reduced spacing between lyrics
                                horizontal: 20,
                              ),
                              padding:
                                  const EdgeInsets.all(12), // Reduced padding
                              decoration: BoxDecoration(
                                color:
                                    Colors.transparent, // No background color
                                borderRadius: BorderRadius.circular(10),
                                // No border for cleaner look
                              ),
                              child: Text(
                                lyrics[index]
                                    .text
                                    .toUpperCase(), // Ethnocentric looks better in uppercase
                                style: TextStyle(
                                  fontFamily:
                                      'Ethnocentric', // Use Ethnocentric font
                                  color: isCurrentLine
                                      ? const Color(
                                          0xFFE955BB) // Pink highlight for active
                                      : Colors.grey.withOpacity(
                                          0.7), // Grey for inactive
                                  fontSize: isCurrentLine
                                      ? 18
                                      : 15, // Smaller font sizes
                                  fontWeight: isCurrentLine
                                      ? FontWeight.bold
                                      : FontWeight.w300,
                                  letterSpacing: isCurrentLine
                                      ? 3.0
                                      : 1.5, // Reduced letter spacing
                                  wordSpacing: isCurrentLine
                                      ? 2.0
                                      : 1.0, // Reduced word spacing
                                  height: 1.3, // Reduced line height
                                  shadows: isCurrentLine
                                      ? [
                                          Shadow(
                                            color:
                                                Colors.black.withOpacity(0.8),
                                            offset: const Offset(2, 2),
                                            blurRadius: 4,
                                          ),
                                          Shadow(
                                            color: const Color(0xFFE955BB)
                                                .withOpacity(0.5),
                                            offset: const Offset(0, 0),
                                            blurRadius: 8,
                                          ),
                                        ]
                                      : [
                                          Shadow(
                                            color:
                                                Colors.black.withOpacity(0.5),
                                            offset: const Offset(1, 1),
                                            blurRadius: 2,
                                          ),
                                        ],
                                ),
                                textAlign: TextAlign.center,
                              ),
                            );
                          },
                        ),
                ),

                // Modern Music Player Controls (moved below lyrics, no container)
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 20), // Increased from 5 to 15 to move player up
                  child: Column(
                    children: [
                      // Progress bar
                      Column(
                        children: [
                          SliderTheme(
                            data: SliderTheme.of(context).copyWith(
                              activeTrackColor: const Color(0xFFE955BB),
                              inactiveTrackColor: Colors.grey.shade700,
                              thumbColor: const Color(0xFFE955BB),
                              overlayColor:
                                  const Color(0xFFE955BB).withOpacity(0.2),
                              trackHeight: 3,
                              thumbShape: const RoundSliderThumbShape(
                                  enabledThumbRadius: 6),
                            ),
                            child: Slider(
                              value: totalDuration.inMilliseconds > 0
                                  ? currentPosition.inMilliseconds
                                      .clamp(0, totalDuration.inMilliseconds)
                                      .toDouble()
                                  : 0.0,
                              max: totalDuration.inMilliseconds.toDouble(),
                              onChangeStart: (value) {
                                setState(() {
                                  isDragging = true;
                                });
                              },
                              onChanged: (value) {
                                setState(() {
                                  currentPosition =
                                      Duration(milliseconds: value.toInt());
                                });
                              },
                              onChangeEnd: (value) async {
                                final seekPosition =
                                    Duration(milliseconds: value.toInt());
                                await seekToPosition(seekPosition);

                                // If the player was stopped/completed, resume playback
                                if (!isPlaying &&
                                    seekPosition < totalDuration) {
                                  print("🎵 Resuming playback after seek...");
                                  await player.resume();
                                }

                                setState(() {
                                  isDragging = false;
                                });
                              },
                            ),
                          ),
                          // Time labels
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  formatDuration(currentPosition),
                                  style: const TextStyle(
                                      color: Colors.grey, fontSize: 12),
                                ),
                                Text(
                                  formatDuration(totalDuration),
                                  style: const TextStyle(
                                      color: Colors.grey, fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10), // Reduced from 20 to 10
                      // Control buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Previous 10s button
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade800,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              onPressed: () async {
                                await seekBackward();
                              },
                              icon: const Icon(Icons.replay_10,
                                  color: Colors.white, size: 24),
                              padding: const EdgeInsets.all(12),
                            ),
                          ),
                          // Play/Pause button
                          Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFE955BB),
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              onPressed: () async {
                                print(
                                    "🎵 Play/Pause button pressed - isPlaying: $isPlaying, currentPosition: ${currentPosition.inSeconds}s, totalDuration: ${totalDuration.inSeconds}s");

                                if (isPlaying) {
                                  await togglePlayPause();
                                } else {
                                  // If not playing, check various states
                                  if (totalDuration == Duration.zero) {
                                    // Audio not loaded yet, start from beginning
                                    print(
                                        "🎵 Audio not loaded, starting fresh...");
                                    startAudio();
                                  } else if (currentPosition >= totalDuration) {
                                    // If at the end, restart from beginning
                                    print("🎵 Restarting from beginning...");
                                    await player.seek(Duration.zero);
                                    await player.resume();
                                  } else if (currentPosition == Duration.zero) {
                                    // If at zero, could start fresh or resume
                                    print(
                                        "🎵 Starting audio from beginning...");
                                    startAudio();
                                  } else {
                                    // Resume from current position
                                    print(
                                        "🎵 Resuming from current position...");
                                    await togglePlayPause();
                                  }
                                }
                              },
                              icon: Icon(
                                isPlaying ? Icons.pause : Icons.play_arrow,
                                color: Colors.white,
                                size: 32,
                              ),
                              padding: const EdgeInsets.all(16),
                            ),
                          ),
                          // Next 10s button
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade800,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              onPressed: () async {
                                await seekForward();
                              },
                              icon: const Icon(Icons.forward_10,
                                  color: Colors.white, size: 24),
                              padding: const EdgeInsets.all(12),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Debug info
                if (lyrics.isEmpty)
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Text(
                          'Debug: No lyrics loaded',
                          style: TextStyle(color: Colors.red, fontSize: 16),
                        ),
                        Text(
                          'isLoading: $isLoading',
                          style: TextStyle(color: Colors.grey, fontSize: 14),
                        ),
                        Text(
                          'Lyrics file: ${widget.lyricsFile?.path ?? "Using assets"}',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
    );
  }
}
