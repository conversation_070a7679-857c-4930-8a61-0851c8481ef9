import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'lyric_screen.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({Key? key}) : super(key: key);

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  File? selectedLyricsFile;
  File? selectedAudioFile;
  File? selectedImageFile;
  String username = '';

  final ImagePicker _imagePicker = ImagePicker();
  final TextEditingController _usernameController = TextEditingController();
  bool isLoading = false;

  Future<void> _pickLyricsFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null) {
        setState(() {
          selectedLyricsFile = File(result.files.single.path!);
        });

        // Validate JSON format
        await _validateLyricsFile();
      }
    } catch (e) {
      _showErrorDialog('Error selecting lyrics file: $e');
    }
  }

  Future<void> _validateLyricsFile() async {
    if (selectedLyricsFile == null) return;

    try {
      final jsonString = await selectedLyricsFile!.readAsString();
      final Map<String, dynamic> data = json.decode(jsonString);

      // Check if it has the expected structure
      if (data.containsKey('lyrics') && data.containsKey('countdown')) {
        final lyricsData = data['lyrics']?['data'];
        if (lyricsData is List && lyricsData.isNotEmpty) {
          // Check if lyrics have required fields
          for (var item in lyricsData) {
            if (!item.containsKey('text') || !item.containsKey('start_time')) {
              throw Exception(
                  'Invalid lyrics format. Each item should have "text" and "start_time" fields.');
            }
          }
          _showSuccessSnackBar('Lyrics file validated successfully!');
        } else {
          throw Exception('No lyrics data found in the file.');
        }
      } else {
        // Try old format (simple array)
        final List<dynamic> oldFormat = json.decode(jsonString);
        for (var item in oldFormat) {
          if (!item.containsKey('time') || !item.containsKey('line')) {
            throw Exception(
                'Invalid lyrics format. Each item should have "time" and "line" fields.');
          }
        }
        _showSuccessSnackBar('Lyrics file validated successfully!');
      }
    } catch (e) {
      setState(() {
        selectedLyricsFile = null;
      });
      _showErrorDialog('Invalid lyrics format: $e');
    }
  }

  Future<void> _pickAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
      );

      if (result != null) {
        setState(() {
          selectedAudioFile = File(result.files.single.path!);
        });
        _showSuccessSnackBar('Audio file selected successfully!');
      }
    } catch (e) {
      _showErrorDialog('Error selecting audio file: $e');
    }
  }

  Future<void> _pickImageFile() async {
    try {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Select Image Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromSource(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromSource(ImageSource.camera);
                },
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      _showErrorDialog('Error selecting image: $e');
    }
  }

  Future<void> _pickImageFromSource(ImageSource source) async {
    final XFile? image = await _imagePicker.pickImage(
      source: source,
      maxWidth: 300,
      maxHeight: 300,
      imageQuality: 80,
    );

    if (image != null) {
      setState(() {
        selectedImageFile = File(image.path);
      });
      _showSuccessSnackBar('Profile image selected successfully!');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _navigateToLyricScreen() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Navigate to LyricScreen with all data
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LyricScreen(
            lyricsFile: selectedLyricsFile,
            audioFile: selectedAudioFile,
            imageFile: selectedImageFile,
            username: username.trim(), // Pass username
          ),
        ),
      );
    } catch (e) {
      _showErrorDialog('Error: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  bool get _allFilesSelected =>
      selectedLyricsFile != null &&
      selectedAudioFile != null &&
      selectedImageFile != null &&
      username.trim().isNotEmpty;

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Files'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Create Your Lyric Video',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),

            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: username.trim().isNotEmpty
                      ? Colors.green
                      : Colors.grey.shade300,
                  width: username.trim().isNotEmpty ? 2 : 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: username.trim().isNotEmpty
                            ? Colors.green
                            : Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.person,
                        color: username.trim().isNotEmpty
                            ? Colors.white
                            : Colors.grey.shade600,
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Username',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: _usernameController,
                            onChanged: (value) {
                              setState(() {
                                username = value;
                              });
                            },
                            decoration: InputDecoration(
                              hintText: 'Enter your username',
                              prefixText: '@',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            maxLength: 20,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Lyrics File Selection
            _buildFileSelectionCard(
              title: 'Lyrics File (JSON)',
              subtitle: 'Select a JSON file with lyrics and timing',
              icon: Icons.text_fields,
              file: selectedLyricsFile,
              onTap: _pickLyricsFile,
            ),

            const SizedBox(height: 16),

            // Audio File Selection
            _buildFileSelectionCard(
              title: 'Audio File',
              subtitle: 'Select an audio file (MP3, WAV, etc.)',
              icon: Icons.audio_file,
              file: selectedAudioFile,
              onTap: _pickAudioFile,
            ),

            const SizedBox(height: 16),

            // Image File Selection
            _buildFileSelectionCard(
              title: 'Profile Image',
              subtitle: 'Select or capture a profile image',
              icon: Icons.image,
              file: selectedImageFile,
              onTap: _pickImageFile,
            ),

            const Spacer(),

            // Generate Video Button
            ElevatedButton(
              onPressed: _allFilesSelected && !isLoading
                  ? _navigateToLyricScreen
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                        SizedBox(width: 10),
                        Text('Processing...'),
                      ],
                    )
                  : const Text(
                      'Create Lyric Video',
                      style: TextStyle(fontSize: 18),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required File? file,
    required VoidCallback onTap,
  }) {
    final bool isSelected = file != null;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected ? Colors.green : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.green : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: isSelected ? Colors.white : Colors.grey.shade600,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      isSelected
                          ? file.path.split(Platform.pathSeparator).last
                          : subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: isSelected ? Colors.green : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                isSelected ? Icons.check_circle : Icons.add_circle_outline,
                color: isSelected ? Colors.green : Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
