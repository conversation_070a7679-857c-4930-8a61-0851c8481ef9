import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'lyrics_data.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:camera/camera.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';

class LyricScreen extends StatefulWidget {
  final File? lyricsFile;
  final File? audioFile;
  final File? imageFile;
  final String? username;

  const LyricScreen({
    Key? key,
    this.lyricsFile,
    this.audioFile,
    this.imageFile,
    this.username,
  }) : super(key: key);

  @override
  State<LyricScreen> createState() => _LyricScreenState();
}

class _LyricScreenState extends State<LyricScreen> {
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<PlayerState>? _stateSubscription;

  List<LyricLine> lyrics = [];
  SongData? songData;

  int currentLine = 0;
  late AudioPlayer player;
  final ItemScrollController scrollController = ItemScrollController();

  File? _profileImage;
  final ImagePicker _picker = ImagePicker();

  bool isPlaying = false;
  bool isLoading = true;
  bool isRecording = false;
  String? recordedVideoPath;

  String username = "username_here";
  bool isGeneratingVideo = false;
  final TextEditingController _usernameController = TextEditingController();

  CameraController? _cameraController;
  List<CameraDescription>? cameras;

  @override
  void initState() {
    super.initState();
    player = AudioPlayer();
    _initializeCamera();

    print("=== LyricScreen Init Debug ===");
    print("Uploaded username: ${widget.username}");
    print("Uploaded image file: ${widget.imageFile?.path}");

    // Set username - prioritize uploaded data
    if (widget.username != null && widget.username!.isNotEmpty) {
      print("Using uploaded username: ${widget.username}");
      setState(() {
        username = widget.username!;
      });
      _saveUsername(widget.username!);
    } else {
      print("No uploaded username, loading from preferences");
      _loadUsername();
    }

    // Set profile image - prioritize uploaded data
    if (widget.imageFile != null) {
      print("Using uploaded image: ${widget.imageFile!.path}");
      setState(() {
        _profileImage = widget.imageFile;
      });
      _saveImagePath(widget.imageFile!.path);
    } else {
      print("No uploaded image, loading from preferences");
      _loadImagePath();
    }

    loadLyrics();
  }

  Future<void> _runFFmpegCommand(String command) async {
    try {
      print('🎬 Starting FFmpeg with command: $command');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        print('✅ FFmpeg finished successfully');
      } else {
        final logs = await session.getLogs();
        final errorMessage = logs.map((log) => log.getMessage()).join('\n');
        print('❌ FFmpeg failed with return code: $returnCode');
        print('Error details: $errorMessage');
        throw Exception('FFmpeg failed: $errorMessage');
      }
    } catch (e) {
      print('❌ FFmpeg execution error: $e');
      throw Exception('FFmpeg failed: $e');
    }
  }

  Future<void> loadLyrics() async {
    try {
      String jsonString;

      if (widget.lyricsFile != null) {
        jsonString = await widget.lyricsFile!.readAsString();
      } else {
        jsonString = await rootBundle.loadString('assets/lyrics.json');
      }

      // Try new format first (with "lyrics" and "countdown")
      try {
        final Map<String, dynamic> data = json.decode(jsonString);
        if (data.containsKey('lyrics') && data.containsKey('countdown')) {
          songData = SongData.fromJson(data);
          setState(() {
            // lyrics data is dynamic
            lyrics = songData!.lyrics;
            isLoading = false;
          });
          return;
        }
      } catch (e) {
        print("New format parsing failed, trying old format: $e");
      }

      // Fall back to old format
      final List<dynamic> data = json.decode(jsonString);
      lyrics = data.map((item) => LyricLine.fromJson(item)).toList();
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print("Error loading lyrics: $e");
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading lyrics: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _initializeCamera() async {
    try {
      cameras = await availableCameras();
    } catch (e) {
      print("Error initializing camera: $e");
    }
  }

  Future<void> _saveUsername(String newUsername) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('username', newUsername);
  }

  Future<void> _loadUsername() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      username = prefs.getString('username') ?? 'username_here';
    });
  }

  Future<void> _saveImagePath(String imagePath) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('profile_image_path', imagePath);
  }

  Future<void> _loadImagePath() async {
    final prefs = await SharedPreferences.getInstance();
    final imagePath = prefs.getString('profile_image_path');
    if (imagePath != null && File(imagePath).existsSync()) {
      setState(() {
        _profileImage = File(imagePath);
      });
    }
  }

  void startAudio() async {
    setState(() {
      isLoading = true;
    });

    try {
      await player.stop();

      String audioPath = "";

      if (widget.audioFile != null) {
        audioPath = widget.audioFile!.path;

        if (audioPath.endsWith(".flac")) {
          // Convert FLAC to MP3 for playback
          final mp3Path = audioPath.replaceAll(".flac", ".mp3");
          print("🎵 Converting .flac to .mp3 for playback...");

          await _runFFmpegCommand('-i "$audioPath" "$mp3Path"');

          if (File(mp3Path).existsSync()) {
            audioPath = mp3Path;
            print("✅ Converted FLAC to MP3: $audioPath");
          } else {
            throw Exception("MP3 conversion failed");
          }
        }
      } else {
        // Use dummy fallback
        final byteData = await rootBundle.load('assets/dummy_audio.mp3');
        final file =
            File('${(await getTemporaryDirectory()).path}/dummy_audio.mp3');
        await file.writeAsBytes(byteData.buffer.asUint8List());
        audioPath = file.path;
      }

      print("🎵 Playing audio from: $audioPath");

      await player.play(DeviceFileSource(audioPath));

      setState(() {
        isPlaying = true;
        isLoading = false;
      });

      _setupAudioListeners();
    } catch (e) {
      print("Error in startAudio(): $e");
      setState(() {
        isLoading = false;
        isPlaying = false;
      });
    }
  }

  void _setupAudioListeners() {
    _positionSubscription?.cancel();
    _stateSubscription?.cancel();

    _positionSubscription =
        player.onPositionChanged.listen((Duration position) {
      for (int i = 0; i < lyrics.length; i++) {
        if (position.inSeconds >= lyrics[i].time &&
            (i == lyrics.length - 1 ||
                position.inSeconds < lyrics[i + 1].time)) {
          if (currentLine != i) {
            setState(() {
              currentLine = i;
            });
            scrollController.scrollTo(
              index: i,
              duration: const Duration(milliseconds: 300),
            );
          }
          break;
        }
      }
    });

    _stateSubscription =
        player.onPlayerStateChanged.listen((PlayerState state) {
      setState(() {
        isPlaying = state == PlayerState.playing;
      });
    });
  }

  Future<void> togglePlayPause() async {
    if (isPlaying) {
      await player.pause();
    } else {
      await player.resume();
    }
  }

  Future<void> _generateVideo() async {
    setState(() {
      isGeneratingVideo = true;
    });

    try {
      await _createFFmpegVideo();
    } catch (e) {
      print("Video generation error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Video generation failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isGeneratingVideo = false;
      });
    }
  }

  Future<void> _createFFmpegVideo() async {
    final Directory tempDir = await getTemporaryDirectory();
    final String outputPath =
        '${tempDir.path}/lyric_video_${DateTime.now().millisecondsSinceEpoch}.mp4';

    print('🎬 Creating video at: $outputPath');

    try {
      // Get audio duration
      Duration audioDuration = await _getAudioDurationFromFile();
      print('🎵 Audio duration: ${audioDuration.inSeconds}s');

      // Get audio file path
      String audioPath;
      if (widget.audioFile != null) {
        audioPath = widget.audioFile!.path;
      } else {
        // Copy asset audio to temp directory
        final byteData = await rootBundle.load('assets/dummy_audio.mp3');
        audioPath = '${tempDir.path}/audio.mp3';
        await File(audioPath).writeAsBytes(byteData.buffer.asUint8List());
      }

      // Generate properly escaped lyric commands
      String lyricCommands = _generateLyricDrawtextCommands();

      // Simple FFmpeg command - saves locally in app directory
      final String ffmpegCommand =
          '-f lavfi -i color=black:s=1080x1920:d=${audioDuration.inSeconds} '
          '-i "$audioPath" '
          '-vf "$lyricCommands" '
          '-c:v libx264 -preset ultrafast -crf 28 '
          '-c:a aac -b:a 128k '
          '-t ${audioDuration.inSeconds} '
          '-pix_fmt yuv420p '
          '-y "$outputPath"';

      print("🎬 FFmpeg command: $ffmpegCommand");

      await _runFFmpegCommand(ffmpegCommand);

      // Check if video was created successfully
      if (File(outputPath).existsSync()) {
        print('✅ Video created successfully at: $outputPath');
        final fileSize = File(outputPath).lengthSync();
        print(
            '📁 Video file size: ${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB');
        _showVideoSuccessDialog(outputPath);
      } else {
        throw Exception("Video file was not created");
      }
    } catch (e) {
      print("❌ Video generation error: $e");
      rethrow;
    }
  }

  String _generateLyricDrawtextCommands() {
    List<String> drawTextCommands = [];

    // Simple text escaping for FFmpeg
    String escapeFFmpegText(String text) {
      return text
          .replaceAll("'", "\\'")
          .replaceAll(":", "\\:")
          .replaceAll("[", "\\[")
          .replaceAll("]", "\\]")
          .replaceAll("(", "\\(")
          .replaceAll(")", "\\)");
    }

    for (int i = 0; i < lyrics.length; i++) {
      final lyric = lyrics[i];
      final startTime = lyric.time.toDouble();
      final endTime = i + 1 < lyrics.length
          ? lyrics[i + 1].time.toDouble()
          : startTime + 5.0;

      final escapedText = escapeFFmpegText(lyric.text);

      drawTextCommands.add("drawtext=text='$escapedText'"
          ":fontfile=/system/fonts/Roboto-Bold.ttf"
          ":fontsize=48"
          ":fontcolor=white"
          ":x=(w-text_w)/2"
          ":y=h*0.7"
          ":shadowcolor=black"
          ":shadowx=2"
          ":shadowy=2"
          ":enable='between(t,$startTime,$endTime)'");
    }

    return drawTextCommands.join(',');
  }

  Future<Duration> _getAudioDurationFromFile() async {
    final tempPlayer = AudioPlayer();

    if (widget.audioFile != null) {
      await tempPlayer.setSource(DeviceFileSource(widget.audioFile!.path));
    } else {
      final byteData = await rootBundle.load('assets/dummy_audio.mp3');
      final file =
          File('${(await getTemporaryDirectory()).path}/temp_audio.mp3');
      await file.writeAsBytes(byteData.buffer.asUint8List());
      await tempPlayer.setSource(DeviceFileSource(file.path));
    }

    final duration = await tempPlayer.getDuration();
    await tempPlayer.dispose();

    return duration ?? const Duration(seconds: 30);
  }

  void _showVideoSuccessDialog(String videoPath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Video Created!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Your lyric video for "@$username" is ready!'),
            const SizedBox(height: 10),
            Text('Saved at: $videoPath'),
            const SizedBox(height: 10),
            const Text('The video is saved locally in your app folder.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset to beginning
              setState(() {
                currentLine = 0;
              });
              player.seek(Duration.zero);
            },
            child: const Text('OK'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _shareVideo(videoPath);
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  Future<void> _shareVideo(String videoPath) async {
    try {
      await Share.shareXFiles([XFile(videoPath)],
          text: 'Check out my lyric video created with Melodyze! @$username');
    } catch (e) {
      print('Error sharing video: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing video: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    player.dispose();
    _usernameController.dispose();
    _positionSubscription?.cancel();
    _stateSubscription?.cancel();
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Lyric Video', style: TextStyle(color: Colors.white)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.videocam, color: Colors.white),
            onPressed: isGeneratingVideo ? null : _generateVideo,
          ),
        ],
      ),
      body: isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.blue),
            )
          : Column(
              children: [
                // Profile section
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 60,
                        backgroundImage: _profileImage != null
                            ? FileImage(_profileImage!)
                            : const AssetImage('assets/dummy_dp.jpg')
                                as ImageProvider,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        '@$username',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // Play controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(
                        isPlaying ? Icons.pause : Icons.play_arrow,
                        color: Colors.white,
                        size: 40,
                      ),
                      onPressed: () {
                        if (isPlaying) {
                          togglePlayPause();
                        } else {
                          startAudio();
                        }
                      },
                    ),
                  ],
                ),

                // Generate video button
                if (isGeneratingVideo)
                  const Padding(
                    padding: EdgeInsets.all(20),
                    child: Column(
                      children: [
                        CircularProgressIndicator(color: Colors.blue),
                        SizedBox(height: 10),
                        Text(
                          'Generating video...',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: ElevatedButton.icon(
                      onPressed: _generateVideo,
                      icon: const Icon(Icons.video_library),
                      label: const Text('Generate Lyric Video'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 30,
                          vertical: 15,
                        ),
                      ),
                    ),
                  ),

                // Lyrics display
                Expanded(
                  child: lyrics.isEmpty
                      ? const Center(
                          child: Text(
                            'No lyrics available',
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                      : ScrollablePositionedList.builder(
                          itemScrollController: scrollController,
                          itemCount: lyrics.length,
                          itemBuilder: (context, index) {
                            final isCurrentLine = index == currentLine;
                            return Container(
                              margin: const EdgeInsets.symmetric(
                                vertical: 8,
                                horizontal: 20,
                              ),
                              padding: const EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                color: isCurrentLine
                                    ? Colors.blue.withOpacity(0.3)
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(10),
                                border: isCurrentLine
                                    ? Border.all(color: Colors.blue, width: 2)
                                    : null,
                              ),
                              child: Text(
                                lyrics[index].text,
                                style: TextStyle(
                                  color: isCurrentLine
                                      ? Colors.white
                                      : Colors.grey,
                                  fontSize: isCurrentLine ? 18 : 16,
                                  fontWeight: isCurrentLine
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }
}
