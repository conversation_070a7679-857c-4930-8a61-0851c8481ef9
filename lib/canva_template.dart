import 'dart:ui' as ui;
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:path_provider/path_provider.dart';

// Canva Template Configuration
class CanvaTemplate {
  final String templateId;
  final String name;
  final CanvaLayout layout;
  final CanvaColors colors;
  final CanvaTypography typography;
  final List<CanvaElement> elements;

  CanvaTemplate({
    required this.templateId,
    required this.name,
    required this.layout,
    required this.colors,
    required this.typography,
    required this.elements,
  });

  factory CanvaTemplate.fromJson(Map<String, dynamic> json) {
    return CanvaTemplate(
      templateId: json['templateId'] ?? '',
      name: json['name'] ?? '',
      layout: CanvaLayout.fromJson(json['layout'] ?? {}),
      colors: CanvaColors.fromJson(json['colors'] ?? {}),
      typography: CanvaTypography.fromJson(json['typography'] ?? {}),
      elements: (json['elements'] as List? ?? [])
          .map((e) => CanvaElement.fromJson(e))
          .toList(),
    );
  }
}

class CanvaLayout {
  final double width;
  final double height;
  final String backgroundType; // 'gradient', 'solid', 'image'
  final List<Color> backgroundColors;
  final String? backgroundImage;

  CanvaLayout({
    this.width = 1080,
    this.height = 1920,
    this.backgroundType = 'gradient',
    this.backgroundColors = const [],
    this.backgroundImage,
  });

  factory CanvaLayout.fromJson(Map<String, dynamic> json) {
    return CanvaLayout(
      width: (json['width'] ?? 1080).toDouble(),
      height: (json['height'] ?? 1920).toDouble(),
      backgroundType: json['backgroundType'] ?? 'gradient',
      backgroundColors: (json['backgroundColors'] as List? ?? [])
          .map((c) => Color(int.parse(c.toString().replaceFirst('#', '0xFF'))))
          .toList(),
      backgroundImage: json['backgroundImage'],
    );
  }
}

class CanvaColors {
  final Color primary;
  final Color secondary;
  final Color accent;
  final Color text;
  final Color highlight;

  CanvaColors({
    this.primary = const Color(0xFF0F0F23),
    this.secondary = const Color(0xFF1A1A2E),
    this.accent = const Color(0xFFE955BB),
    this.text = Colors.white,
    this.highlight = const Color(0xFFE955BB),
  });

  factory CanvaColors.fromJson(Map<String, dynamic> json) {
    return CanvaColors(
      primary: Color(
          int.parse((json['primary'] ?? '#0F0F23').replaceFirst('#', '0xFF'))),
      secondary: Color(int.parse(
          (json['secondary'] ?? '#1A1A2E').replaceFirst('#', '0xFF'))),
      accent: Color(
          int.parse((json['accent'] ?? '#E955BB').replaceFirst('#', '0xFF'))),
      text: Color(
          int.parse((json['text'] ?? '#FFFFFF').replaceFirst('#', '0xFF'))),
      highlight: Color(int.parse(
          (json['highlight'] ?? '#E955BB').replaceFirst('#', '0xFF'))),
    );
  }
}

class CanvaTypography {
  final String fontFamily;
  final String lyricFontFamily; // Separate font for lyrics
  final double titleSize;
  final double subtitleSize;
  final double bodySize;
  final double lyricSize;
  final FontWeight titleWeight;
  final FontWeight bodyWeight;

  CanvaTypography({
    this.fontFamily = 'Roboto',
    this.lyricFontFamily = 'Ethnocentric', // Ethnocentric font for lyrics
    this.titleSize = 48,
    this.subtitleSize = 28,
    this.bodySize = 24,
    this.lyricSize = 52,
    this.titleWeight = FontWeight.bold,
    this.bodyWeight = FontWeight.w500,
  });

  factory CanvaTypography.fromJson(Map<String, dynamic> json) {
    return CanvaTypography(
      fontFamily: json['fontFamily'] ?? 'Roboto',
      lyricFontFamily: json['lyricFontFamily'] ?? 'Ethnocentric',
      titleSize: (json['titleSize'] ?? 48).toDouble(),
      subtitleSize: (json['subtitleSize'] ?? 28).toDouble(),
      bodySize: (json['bodySize'] ?? 24).toDouble(),
      lyricSize: (json['lyricSize'] ?? 52).toDouble(),
      titleWeight: _parseWeight(json['titleWeight']),
      bodyWeight: _parseWeight(json['bodyWeight']),
    );
  }

  static FontWeight _parseWeight(dynamic weight) {
    if (weight is String) {
      switch (weight.toLowerCase()) {
        case 'bold':
          return FontWeight.bold;
        case 'normal':
          return FontWeight.normal;
        case 'light':
          return FontWeight.w300;
        case 'medium':
          return FontWeight.w500;
        case 'semibold':
          return FontWeight.w600;
        default:
          return FontWeight.w500;
      }
    }
    return FontWeight.w500;
  }
}

class CanvaElement {
  final String type; // 'profile', 'username', 'lyrics', 'decoration'
  final CanvaPosition position;
  final CanvaStyle style;
  final String? content;
  final bool isDynamic;

  CanvaElement({
    required this.type,
    required this.position,
    required this.style,
    this.content,
    this.isDynamic = true,
  });

  factory CanvaElement.fromJson(Map<String, dynamic> json) {
    return CanvaElement(
      type: json['type'] ?? '',
      position: CanvaPosition.fromJson(json['position'] ?? {}),
      style: CanvaStyle.fromJson(json['style'] ?? {}),
      content: json['content'],
      isDynamic: json['isDynamic'] ?? true,
    );
  }
}

class CanvaPosition {
  final double x;
  final double y;
  final double width;
  final double height;
  final String alignment; // 'center', 'left', 'right'

  CanvaPosition({
    this.x = 0,
    this.y = 0,
    this.width = 100,
    this.height = 100,
    this.alignment = 'center',
  });

  factory CanvaPosition.fromJson(Map<String, dynamic> json) {
    return CanvaPosition(
      x: (json['x'] ?? 0).toDouble(),
      y: (json['y'] ?? 0).toDouble(),
      width: (json['width'] ?? 100).toDouble(),
      height: (json['height'] ?? 100).toDouble(),
      alignment: json['alignment'] ?? 'center',
    );
  }
}

class CanvaStyle {
  final Color? color;
  final Color? backgroundColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final List<Shadow>? shadows;
  final double? borderRadius;
  final Color? borderColor;
  final double? borderWidth;
  final List<Color>? gradientColors;
  final double? opacity;

  CanvaStyle({
    this.color,
    this.backgroundColor,
    this.fontSize,
    this.fontWeight,
    this.shadows,
    this.borderRadius,
    this.borderColor,
    this.borderWidth,
    this.gradientColors,
    this.opacity,
  });

  factory CanvaStyle.fromJson(Map<String, dynamic> json) {
    return CanvaStyle(
      color: json['color'] != null
          ? Color(int.parse(json['color'].replaceFirst('#', '0xFF')))
          : null,
      backgroundColor: json['backgroundColor'] != null
          ? Color(int.parse(json['backgroundColor'].replaceFirst('#', '0xFF')))
          : null,
      fontSize: json['fontSize']?.toDouble(),
      fontWeight: CanvaTypography._parseWeight(json['fontWeight']),
      borderRadius: json['borderRadius']?.toDouble(),
      borderColor: json['borderColor'] != null
          ? Color(int.parse(json['borderColor'].replaceFirst('#', '0xFF')))
          : null,
      borderWidth: json['borderWidth']?.toDouble(),
      opacity: json['opacity']?.toDouble(),
      shadows: json['shadows'] != null
          ? (json['shadows'] as List)
              .map((s) => Shadow(
                    color:
                        Color(int.parse(s['color'].replaceFirst('#', '0xFF'))),
                    offset: Offset(s['offsetX']?.toDouble() ?? 0,
                        s['offsetY']?.toDouble() ?? 0),
                    blurRadius: s['blurRadius']?.toDouble() ?? 0,
                  ))
              .toList()
          : null,
      gradientColors: json['gradientColors'] != null
          ? (json['gradientColors'] as List)
              .map((c) => Color(int.parse(c.replaceFirst('#', '0xFF'))))
              .toList()
          : null,
    );
  }
}

// Dynamic Template Renderer
class CanvaTemplateRenderer {
  // Static cache for video frames
  static Map<int, ui.Image?> _videoFrameCache = {};
  static bool _isExtractingFrames = false;

  // Initialize video frame extraction (call this when app starts)
  static Future<void> initializeVideoFrames() async {
    if (_videoFrameCache.isNotEmpty) {
      return; // Already have frames
    }

    if (_isExtractingFrames) {
      // Wait for existing extraction to complete
      while (_isExtractingFrames) {
        await Future.delayed(Duration(milliseconds: 500));
      }
      return;
    }

    await _extractVideoFrames();
  }

  // Force clear cache and re-extract for debugging
  static Future<void> forceReinitializeVideoFrames() async {
    _videoFrameCache.clear();
    _isExtractingFrames = false;
    await initializeVideoFrames();
  }

  static Future<void> renderFrame({
    required Canvas canvas,
    required CanvaTemplate template,
    required String username,
    required File? profileImage,
    required List<String> lyrics,
    required int currentLyricIndex,
    required int frameIndex,
  }) async {
    // Render background
    await _renderBackground(canvas, template.layout);

    // Extract video frames if not already done (non-blocking)
    if (!_isExtractingFrames && _videoFrameCache.isEmpty) {
      _extractVideoFrames(); // Don't await to avoid blocking rendering
    }

    // Render all template elements with dynamic data
    for (final element in template.elements) {
      switch (element.type) {
        case 'profile':
          await _renderProfile(canvas, element, profileImage);
          break;
        case 'main_profile':
          await _renderMainProfile(canvas, element, profileImage);
          break;
        case 'song_title':
          await _renderSongTitle(canvas, element, template.typography);
          break;
        case 'song_title_lyrics':
          await _renderSongTitleLyrics(canvas, element, template.typography);
          break;
        case 'artist_name':
          await _renderArtistName(canvas, element, template.typography);
          break;
        case 'username_display':
          await _renderUsernameDisplay(
              canvas, element, username, template.typography);
          break;
        case 'audio_waveform':
          await _renderAudioWaveform(canvas, element, frameIndex, template);
          break;
        case 'video_waves':
          await _renderVideoWaves(canvas, element, frameIndex);
          break;
        case 'topbar_profile':
          await _renderTopBarProfile(canvas, element, profileImage);
          break;
        case 'melodyze_logo':
          await _renderMelodyzeLogo(canvas, element, template.typography);
          break;
        case 'melodyze_logo_badge':
          await _renderMelodyzeLogoBadge(canvas, element);
          break;
        case 'song_info':
          await _renderSongInfo(canvas, element, template.typography);
          break;
        case 'username':
          await _renderUsername(canvas, element, username, template.typography);
          break;
        case 'songname':
          await _renderSongName(canvas, element, template.typography);
          break;
        case 'lyrics':
          await _renderLyrics(
              canvas, element, lyrics, currentLyricIndex, template);
          break;
        case 'decoration':
          await _renderDecoration(canvas, element, frameIndex);
          break;
        case 'title':
          await _renderTitle(canvas, element, template.typography);
          break;
      }
    }
  }

  // Extract video frames from the asset video
  static Future<void> _extractVideoFrames() async {
    _isExtractingFrames = true;

    try {
      final tempDir = await getTemporaryDirectory();
      final assetData =
          await rootBundle.load('assets/MicrosoftTeams-video.mp4');

      final videoFile = File('${tempDir.path}/waves_video.mp4');
      await videoFile.writeAsBytes(assetData.buffer.asUint8List());

      // Extract frames using FFmpeg (optimized for smooth performance)
      final outputDir = '${tempDir.path}/waves_frames';
      await Directory(outputDir).create(recursive: true);

      // Optimized settings: 15fps, moderate quality for better performance
      final command =
          '-i "${videoFile.path}" -vf "fps=30,scale=900:350" -q:v 2 "$outputDir/frame_%04d.png"';

      await FFmpegKit.execute(command).then((session) async {
        final returnCode = await session.getReturnCode();

        if (ReturnCode.isSuccess(returnCode)) {
          // Load extracted frames into cache (limited to 300 for better performance)
          for (int i = 1; i <= 300; i++) {
            final frameFile =
                File('$outputDir/frame_${i.toString().padLeft(4, '0')}.png');
            if (await frameFile.exists()) {
              try {
                final frameBytes = await frameFile.readAsBytes();
                final codec = await ui.instantiateImageCodec(frameBytes);
                final frame = await codec.getNextFrame();
                _videoFrameCache[i - 1] = frame.image;
              } catch (e) {
                // Silent error handling
              }
            } else {
              // No more frames found
              break;
            }
          }
        }
      });
    } catch (e) {
      // Silent error handling
    }

    _isExtractingFrames = false;
  }

  // Main profile with custom border image
  static Future<void> _renderMainProfile(
      Canvas canvas, CanvaElement element, File? profileImage) async {
    final position = element.position;

    try {
      // Load the custom border image
      ui.Image? borderImage;
      try {
        final borderData = await rootBundle.load('assets/profile border.png');
        final borderBytes = borderData.buffer.asUint8List();
        final borderCodec = await ui.instantiateImageCodec(borderBytes);
        final borderFrame = await borderCodec.getNextFrame();
        borderImage = borderFrame.image;
      } catch (e) {
        print('Could not load border image: $e');
      }

      if (profileImage != null) {
        final imageBytes = await profileImage.readAsBytes();
        final codec = await ui.instantiateImageCodec(imageBytes);
        final frame = await codec.getNextFrame();
        final img = frame.image;

        final centerX = position.x + position.width / 2;
        final centerY = position.y + position.height / 2;
        final radius = position.width / 2;

        // Draw glowing border effect (multiple layers for glow)
        final glowColors = [
          Colors.blue.withValues(alpha: 0.8),
          Colors.purple.withValues(alpha: 0.6),
          Colors.cyan.withValues(alpha: 0.4),
        ];

        for (int i = 0; i < glowColors.length; i++) {
          final glowPaint = Paint()
            ..color = glowColors[i]
            ..style = PaintingStyle.stroke
            ..strokeWidth = 12.0 - (i * 3)
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, 15.0 - (i * 3));

          canvas.drawCircle(
            Offset(centerX, centerY),
            radius + 15 + (i * 5),
            glowPaint,
          );
        }

        // Draw circular profile image first
        canvas.save();
        canvas.clipPath(Path()
          ..addOval(Rect.fromLTWH(
              position.x, position.y, position.width, position.height)));
        canvas.drawImageRect(
          img,
          Rect.fromLTWH(0, 0, img.width.toDouble(), img.height.toDouble()),
          Rect.fromLTWH(
              position.x, position.y, position.width, position.height),
          Paint(),
        );
        canvas.restore();

        // Draw the custom border image on top
        if (borderImage != null) {
          // Make the border slightly larger than the profile
          final borderSize = position.width * 1.2;
          final borderX = centerX - borderSize / 2;
          final borderY = centerY - borderSize / 2;

          canvas.drawImageRect(
            borderImage,
            Rect.fromLTWH(0, 0, borderImage.width.toDouble(),
                borderImage.height.toDouble()),
            Rect.fromLTWH(borderX, borderY, borderSize, borderSize),
            Paint(),
          );
        } else {
          // Fallback to white border if image fails to load
          final borderPaint = Paint()
            ..color = Colors.white
            ..style = PaintingStyle.stroke
            ..strokeWidth = 6;
          canvas.drawCircle(Offset(centerX, centerY), radius + 8, borderPaint);
        }
      }
    } catch (e) {
      // Draw default circle with glow
      final centerX = position.x + position.width / 2;
      final centerY = position.y + position.height / 2;
      final radius = position.width / 2;

      // Glow effect
      final glowPaint = Paint()
        ..color = Colors.blue.withValues(alpha: 0.6)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 12
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);
      canvas.drawCircle(Offset(centerX, centerY), radius + 15, glowPaint);

      // Main circle
      canvas.drawCircle(
        Offset(centerX, centerY),
        radius,
        Paint()..color = Colors.grey.shade700,
      );
    }
  }

  // Song title (large text like "FADED")
  static Future<void> _renderSongTitle(
      Canvas canvas, CanvaElement element, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    final painter = TextPainter(
      text: TextSpan(
        text: 'FADED',
        style: TextStyle(
          fontFamily: 'Ethnocentric',
          color: const Color(0xFFFFFFFF), // Pure white
          fontSize: style.fontSize ?? 18,
          fontWeight: FontWeight.bold,
          letterSpacing: 3.0, // Increased letter spacing
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.8),
              offset: const Offset(2, 2),
              blurRadius: 4,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);

    double x = position.x;
    if (position.alignment == 'center') {
      x = position.x + (position.width - painter.width) / 2;
    }
    painter.paint(canvas, Offset(x, position.y));
  }

  // Song title for lyrics section (like "ALIGHT")
  static Future<void> _renderSongTitleLyrics(
      Canvas canvas, CanvaElement element, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    final painter = TextPainter(
      text: TextSpan(
        text: 'ALIGHT',
        style: TextStyle(
          fontFamily: 'Ethnocentric',
          color: style.color ?? Colors.white,
          fontSize: style.fontSize ?? 36,
          fontWeight: FontWeight.bold,
          letterSpacing: 3,
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.8),
              offset: const Offset(2, 2),
              blurRadius: 4,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);

    double x = position.x + (position.width - painter.width) / 2;
    painter.paint(canvas, Offset(x, position.y));
  }

  // Artist name (like "ALAN WALKER")
  static Future<void> _renderArtistName(
      Canvas canvas, CanvaElement element, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    final painter = TextPainter(
      text: TextSpan(
        text: 'ALAN WALKER',
        style: TextStyle(
          fontFamily: 'Ethnocentric',
          color: const Color(0xFFCCCCCC), // Medium gray as specified
          fontSize: style.fontSize ?? 14,
          fontWeight: FontWeight.w500, // Slightly bolder
          letterSpacing: 2.0, // Increased letter spacing
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.6),
              offset: const Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);

    double x = position.x;
    if (position.alignment == 'center') {
      x = position.x + (position.width - painter.width) / 2;
    }
    painter.paint(canvas, Offset(x, position.y));
  }

  // Username display (smaller, at bottom of top section)
  static Future<void> _renderUsernameDisplay(Canvas canvas,
      CanvaElement element, String username, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    final painter = TextPainter(
      text: TextSpan(
        text: username.startsWith('@') ? username : '@$username',
        style: TextStyle(
          color: (style.color ?? Colors.white).withValues(alpha: 0.8),
          fontSize: style.fontSize ?? 24,
          fontWeight: FontWeight.w400,
          letterSpacing: 1,
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.5),
              offset: const Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);

    double x = position.x + (position.width - painter.width) / 2;
    painter.paint(canvas, Offset(x, position.y));
  }

  // Audio waveform visualization (the centerpiece!)
  static Future<void> _renderAudioWaveform(Canvas canvas, CanvaElement element,
      int frameIndex, CanvaTemplate template) async {
    final position = element.position;

    final waveformHeight = position.height;
    final waveformWidth = position.width;
    final centerY = position.y + waveformHeight / 2;

    // Number of bars in the waveform
    const int numBars = 50;
    final barWidth = waveformWidth / numBars;
    final maxBarHeight = waveformHeight * 0.8;

    // Create gradient from orange/red to purple/blue
    final gradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        const Color(0xFFFF6B35), // Orange
        const Color(0xFFFF8E3C), // Orange-red
        const Color(0xFFD63384), // Pink
        const Color(0xFFBA68C8), // Light purple
        const Color(0xFF9C27B0), // Purple
        const Color(0xFF673AB7), // Deep purple
      ],
    );

    final gradientRect =
        Rect.fromLTWH(position.x, position.y, waveformWidth, waveformHeight);
    final gradientShader = gradient.createShader(gradientRect);

    for (int i = 0; i < numBars; i++) {
      final x = position.x + (i * barWidth);

      // Create animated wave effect using sine waves
      final time = frameIndex * 0.1;
      final frequency1 = 0.5 + (i * 0.1);
      final frequency2 = 0.3 + (i * 0.05);

      // Combine multiple sine waves for complex animation
      final wave1 = (1 + math.sin(time + frequency1)) / 2;
      final wave2 = (1 + math.sin(time * 1.5 + frequency2)) / 2;
      final wave3 = (1 + math.sin(time * 0.7 + i * 0.2)) / 2;

      // Mix the waves and add some randomness
      final amplitude =
          (wave1 * 0.4 + wave2 * 0.4 + wave3 * 0.2) * maxBarHeight;
      final barHeight = amplitude.clamp(maxBarHeight * 0.1, maxBarHeight);

      // Draw the bar with gradient
      final barPaint = Paint()
        ..shader = gradientShader
        ..style = PaintingStyle.fill;

      final rect = Rect.fromLTWH(
        x + barWidth * 0.1, // Small gap between bars
        centerY - barHeight / 2,
        barWidth * 0.8,
        barHeight,
      );

      // Round the corners
      final rrect = RRect.fromRectAndRadius(rect, const Radius.circular(2));
      canvas.drawRRect(rrect, barPaint);

      // Add glow effect for some bars
      if (amplitude > maxBarHeight * 0.6) {
        final glowPaint = Paint()
          ..color = (i < numBars / 2 ? Colors.orange : Colors.purple)
              .withValues(alpha: 0.4)
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

        final glowRect = Rect.fromLTWH(
          x + barWidth * 0.1 - 2,
          centerY - barHeight / 2 - 2,
          barWidth * 0.8 + 4,
          barHeight + 4,
        );

        canvas.drawRRect(
          RRect.fromRectAndRadius(glowRect, const Radius.circular(4)),
          glowPaint,
        );
      }
    }

    // Add play button in center
    final playButtonX = position.x + waveformWidth / 2;
    final playButtonY = centerY;
    const playButtonRadius = 30.0;

    // Play button background
    final playButtonPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
        Offset(playButtonX, playButtonY), playButtonRadius, playButtonPaint);

    // Play icon
    final playIconPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    final playIconPath = Path();
    playIconPath.moveTo(playButtonX - 8, playButtonY - 12);
    playIconPath.lineTo(playButtonX + 12, playButtonY);
    playIconPath.lineTo(playButtonX - 8, playButtonY + 12);
    playIconPath.close();

    canvas.drawPath(playIconPath, playIconPaint);
  }

  // Video waves visualization - CLEAN VERSION WITH NO BACKGROUND FRAMES
  static Future<void> _renderVideoWaves(
      Canvas canvas, CanvaElement element, int frameIndex) async {
    final position = element.position;

    // Create clean black background rectangle
    final rect =
        Rect.fromLTWH(position.x, position.y, position.width, position.height);

    // Always draw pure black background first
    final blackBgPaint = Paint()..color = const Color(0xFF000000);
    canvas.drawRect(rect, blackBgPaint);

    // Check if we have video frames cached
    if (_videoFrameCache.isNotEmpty) {
      // Calculate which video frame to show with smooth progressive playback
      final totalFrames = _videoFrameCache.length;
      // Use direct frame mapping for smooth playback (no speed multiplier)
      final currentFrame = frameIndex % totalFrames;
      final videoFrame = _videoFrameCache[currentFrame];

      if (videoFrame != null) {
        // Draw the actual video frame - fill the entire area without cropping
        canvas.drawImageRect(
          videoFrame,
          Rect.fromLTWH(
              0, 0, videoFrame.width.toDouble(), videoFrame.height.toDouble()),
          rect,
          Paint()
            ..filterQuality = FilterQuality.medium // Reduced for better performance
            ..isAntiAlias = false, // Disabled for smoother performance
        );

        return; // Exit early since we successfully rendered the real video
      }
    }

    // Fallback: Simple black screen with minimal loading text
    final loadingPainter = TextPainter(
      text: TextSpan(
        text: 'LOADING...',
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.3),
          fontSize: 16,
          fontWeight: FontWeight.w300,
          letterSpacing: 2,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    loadingPainter.layout(maxWidth: position.width);
    loadingPainter.paint(
      canvas,
      Offset(
        position.x + (position.width - loadingPainter.width) / 2,
        position.y + (position.height - loadingPainter.height) / 2,
      ),
    );
  }

  static Future<void> _renderBackground(
      Canvas canvas, CanvaLayout layout) async {
    final rect = Rect.fromLTWH(0, 0, layout.width, layout.height);

    if (layout.backgroundType == 'gradient' &&
        layout.backgroundColors.isNotEmpty) {
      final gradient = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: layout.backgroundColors.isNotEmpty
            ? layout.backgroundColors
            : [
                const Color(0xFF0F0F23),
                const Color(0xFF1A1A2E),
                const Color(0xFF16213E),
                const Color(0xFF0F0F23),
              ],
      );
      canvas.drawRect(rect, Paint()..shader = gradient.createShader(rect));
    } else {
      canvas.drawRect(
          rect,
          Paint()
            ..color = layout.backgroundColors.isNotEmpty
                ? layout.backgroundColors.first
                : const Color(0xFF0F0F23));
    }
  }

  static Future<void> _renderProfile(
      Canvas canvas, CanvaElement element, File? profileImage) async {
    final position = element.position;
    final style = element.style;

    try {
      // Load the custom border image
      ui.Image? borderImage;
      try {
        final borderData = await rootBundle.load('assets/profile border.png');
        final borderBytes = borderData.buffer.asUint8List();
        final borderCodec = await ui.instantiateImageCodec(borderBytes);
        final borderFrame = await borderCodec.getNextFrame();
        borderImage = borderFrame.image;
      } catch (e) {
        print('Could not load border image: $e');
      }

      if (profileImage != null) {
        final imageBytes = await profileImage.readAsBytes();
        final codec = await ui.instantiateImageCodec(imageBytes);
        final frame = await codec.getNextFrame();
        final img = frame.image;

        final centerX = position.x + position.width / 2;
        final centerY = position.y + position.height / 2;
        final radius = position.width / 2;

        // No glow effects - clean profile look

        // Draw circular profile image
        canvas.save();
        canvas.clipPath(Path()
          ..addOval(Rect.fromLTWH(
              position.x, position.y, position.width, position.height)));
        canvas.drawImageRect(
          img,
          Rect.fromLTWH(0, 0, img.width.toDouble(), img.height.toDouble()),
          Rect.fromLTWH(
              position.x, position.y, position.width, position.height),
          Paint(),
        );
        canvas.restore();

        // Draw the custom border image on top
        if (borderImage != null) {
          final borderSize = position.width;
          final borderX = centerX - borderSize / 2;
          final borderY = centerY - borderSize / 2;

          canvas.drawImageRect(
            borderImage,
            Rect.fromLTWH(0, 0, borderImage.width.toDouble(),
                borderImage.height.toDouble()),
            Rect.fromLTWH(borderX, borderY, borderSize, borderSize),
            Paint(),
          );
        } else {
          // Fallback to colored border if image fails to load
          final borderPaint = Paint()
            ..color = style.borderColor ?? const Color(0xFF00FFFF)
            ..style = PaintingStyle.stroke
            ..strokeWidth = (style.borderWidth ?? 8) + 2; // Thicker border
          canvas.drawCircle(
            Offset(centerX, centerY),
            radius + ((style.borderWidth ?? 8) + 2) / 2,
            borderPaint,
          );
        }
      }
    } catch (e) {
      // Draw default circle with black background instead of grey
      final centerX = position.x + position.width / 2;
      final centerY = position.y + position.height / 2;
      final radius = position.width / 2;

      canvas.drawCircle(
        Offset(centerX, centerY),
        radius,
        Paint()..color = const Color(0xFF000000),
      );
    }
  }

  // Melodyze logo badge - overlay at bottom-right of profile
  static Future<void> _renderMelodyzeLogoBadge(
      Canvas canvas, CanvaElement element) async {
    final position = element.position;
    final style = element.style;

    try {
      // Load the Melodyze logo
      final logoData = await rootBundle.load('assets/logo_melodyze.png');
      final logoBytes = logoData.buffer.asUint8List();
      final logoCodec = await ui.instantiateImageCodec(logoBytes);
      final logoFrame = await logoCodec.getNextFrame();
      final logoImage = logoFrame.image;

      final centerX = position.x + position.width / 2;
      final centerY = position.y + position.height / 2;
      final radius = position.width / 2;

      canvas.save();
      canvas.clipPath(Path()
        ..addOval(Rect.fromLTWH(
            position.x, position.y, position.width, position.height)));

      canvas.drawImageRect(
        logoImage,
        Rect.fromLTWH(
            0, 0, logoImage.width.toDouble(), logoImage.height.toDouble()),
        Rect.fromLTWH(position.x, position.y, position.width, position.height),
        Paint()..filterQuality = FilterQuality.high,
      );
      canvas.restore();

      if (style.borderWidth != null && style.borderColor != null) {
        final borderPaint = Paint()
          ..color = style.borderColor!
          ..style = PaintingStyle.stroke
          ..strokeWidth = style.borderWidth!;
        canvas.drawCircle(
          Offset(centerX, centerY),
          radius,
          borderPaint,
        );
      }
    } catch (e) {
      final centerX = position.x + position.width / 2;
      final centerY = position.y + position.height / 2;
      final radius = position.width / 2;

      final bgPaint = Paint()
        ..color = Colors.black
        ..style = PaintingStyle.fill;
      canvas.drawCircle(Offset(centerX, centerY), radius, bgPaint);

      final borderPaint = Paint()
        ..color = Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawCircle(Offset(centerX, centerY), radius, borderPaint);

      final textPainter = TextPainter(
        text: TextSpan(
          text: 'M',
          style: TextStyle(
            color: Colors.white,
            fontSize: position.width * 0.4,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          centerX - textPainter.width / 2,
          centerY - textPainter.height / 2,
        ),
      );
    }
  }

  static Future<void> _renderUsername(Canvas canvas, CanvaElement element,
      String username, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    final painter = TextPainter(
      text: TextSpan(
        text: element.content
                ?.replaceAll('{username}', username.replaceAll('@', '')) ??
            username.replaceAll('@', ''),
        style: TextStyle(
          color: style.color ?? Colors.white,
          fontSize: style.fontSize ?? typography.titleSize,
          fontWeight: style.fontWeight ?? typography.titleWeight,
          shadows: style.shadows,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);

    double x = position.x;
    if (position.alignment == 'center') {
      x = position.x + (position.width - painter.width) / 2;
    } else if (position.alignment == 'right') {
      x = position.x + position.width - painter.width;
    }

    painter.paint(canvas, Offset(x, position.y));
  }

  static Future<void> _renderSongName(
      Canvas canvas, CanvaElement element, CanvaTypography typography) async {
    final position = element.position;

    final painter = TextPainter(
      text: TextSpan(
        text: 'Faded',
        style: TextStyle(
          fontFamily: 'Ethnocentric',
          color: Colors.blue,
          fontSize: 50,
          fontWeight: FontWeight.bold,
          letterSpacing: 2,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.8),
              offset: Offset(3, 3),
              blurRadius: 6,
            ),
            Shadow(
              color: Colors.blue.withOpacity(0.5),
              offset: Offset(0, 0),
              blurRadius: 12,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);

    double x = position.x;
    if (position.alignment == 'center') {
      x = position.x + (position.width - painter.width) / 2;
    } else if (position.alignment == 'right') {
      x = position.x + position.width - painter.width;
    }

    painter.paint(canvas, Offset(x, position.y));
  }

  static Future<void> _renderTitle(
      Canvas canvas, CanvaElement element, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    final painter = TextPainter(
      text: TextSpan(
        text: element.content ?? 'COVER BY',
        style: TextStyle(
          color: style.color ?? Colors.white.withValues(alpha: 0.8),
          fontSize: style.fontSize ?? typography.subtitleSize,
          fontWeight: style.fontWeight ?? FontWeight.w300,
          letterSpacing: 3,
          shadows: style.shadows,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);

    double x = position.x;
    if (position.alignment == 'center') {
      x = position.x + (position.width - painter.width) / 2;
    }

    painter.paint(canvas, Offset(x, position.y));
  }

  // Top bar profile (smaller version)
  static Future<void> _renderTopBarProfile(
      Canvas canvas, CanvaElement element, File? profileImage) async {
    final position = element.position;
    final style = element.style;

    try {
      if (profileImage != null) {
        final imageBytes = await profileImage.readAsBytes();
        final codec = await ui.instantiateImageCodec(imageBytes);
        final frame = await codec.getNextFrame();
        final img = frame.image;

        // Draw border if specified
        if (style.borderWidth != null && style.borderColor != null) {
          final borderPaint = Paint()
            ..color = style.borderColor!
            ..style = PaintingStyle.stroke
            ..strokeWidth = style.borderWidth!;
          canvas.drawCircle(
            Offset(position.x + position.width / 2,
                position.y + position.height / 2),
            position.width / 2 + style.borderWidth! / 2,
            borderPaint,
          );
        }

        // Draw circular profile image
        canvas.save();
        canvas.clipPath(Path()
          ..addOval(Rect.fromLTWH(
              position.x, position.y, position.width, position.height)));
        canvas.drawImageRect(
          img,
          Rect.fromLTWH(0, 0, img.width.toDouble(), img.height.toDouble()),
          Rect.fromLTWH(
              position.x, position.y, position.width, position.height),
          Paint(),
        );
        canvas.restore();
      }
    } catch (e) {
      // Draw default circle
      canvas.drawCircle(
        Offset(
            position.x + position.width / 2, position.y + position.height / 2),
        position.width / 2,
        Paint()..color = Colors.grey.shade700,
      );
    }
  }

  // Melodyze logo with collaboration text (top bar)
  static Future<void> _renderMelodyzeLogo(
      Canvas canvas, CanvaElement element, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    final painter = TextPainter(
      text: TextSpan(
        text: 'x MELODYZE',
        style: TextStyle(
          color: style.color ?? Colors.white,
          fontSize: style.fontSize ?? 18,
          fontWeight: FontWeight.w600,
          letterSpacing: 1.5,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    painter.layout(maxWidth: position.width);
    painter.paint(canvas, Offset(position.x, position.y));
  }

  // Song info (original artist and cover artist) - Top right section
  static Future<void> _renderSongInfo(
      Canvas canvas, CanvaElement element, CanvaTypography typography) async {
    final position = element.position;
    final style = element.style;

    // Song name (top right)
    final songPainter = TextPainter(
      text: TextSpan(
        text: 'FADED',
        style: TextStyle(
          color: style.color ?? Colors.white,
          fontSize: (style.fontSize ?? 16) + 2,
          fontWeight: FontWeight.bold,
          letterSpacing: 1,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Original artist
    final originalArtistPainter = TextPainter(
      text: TextSpan(
        text: 'ALAN WALKER',
        style: TextStyle(
          color: (style.color ?? Colors.white).withValues(alpha: 0.8),
          fontSize: style.fontSize ?? 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 1,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Cover artist
    final coverArtistPainter = TextPainter(
      text: TextSpan(
        text: 'username',
        style: TextStyle(
          color: (style.color ?? Colors.white).withValues(alpha: 0.9),
          fontSize: style.fontSize ?? 16,
          fontWeight: FontWeight.w500,
          letterSpacing: 1,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    songPainter.layout(maxWidth: position.width);
    originalArtistPainter.layout(maxWidth: position.width);
    coverArtistPainter.layout(maxWidth: position.width);

    // Draw song name (top right)
    songPainter.paint(canvas,
        Offset(position.x + position.width - songPainter.width, position.y));

    // Draw original artist (middle right)
    originalArtistPainter.paint(
        canvas,
        Offset(position.x + position.width - originalArtistPainter.width,
            position.y + 25));

    // Draw cover artist (bottom right)
    coverArtistPainter.paint(
        canvas,
        Offset(position.x + position.width - coverArtistPainter.width,
            position.y + 50));
  }

  static Future<void> _renderLyrics(
      Canvas canvas,
      CanvaElement element,
      List<String> lyrics,
      int currentLyricIndex,
      CanvaTemplate template) async {
    final position = element.position;

    // Ensure we have valid lyrics and index
    if (lyrics.isEmpty) {
      lyrics = [
        'THE CLUB ISN\'T THE BEST',
        'PLACE TO FIND A LOVER',
        'SO THE BAR IS WHERE I GO'
      ];
      currentLyricIndex = 1; // Middle line
    }

    // Clamp currentLyricIndex to valid range
    currentLyricIndex = currentLyricIndex.clamp(0, lyrics.length - 1);

    // Show only 3 lines: previous, current, next - but start showing from middle
    List<String> displayLyrics = [];
    List<bool> isHighlighted = [];

    // Always show 3 slots, but handle edge cases for beginning and end
    if (currentLyricIndex == 0) {
      // At start: empty, current, next
      displayLyrics.add(''); // Empty top line
      displayLyrics.add(lyrics[currentLyricIndex]); // Current in middle
      if (currentLyricIndex + 1 < lyrics.length) {
        displayLyrics.add(lyrics[currentLyricIndex + 1]); // Next
      } else {
        displayLyrics.add(''); // Empty if no next
      }
      isHighlighted.addAll([false, true, false]); // Only middle highlighted
    } else if (currentLyricIndex >= lyrics.length - 1) {
      // At end: previous, current, empty
      displayLyrics.add(lyrics[currentLyricIndex - 1]); // Previous
      displayLyrics.add(lyrics[currentLyricIndex]); // Current in middle
      displayLyrics.add(''); // Empty bottom line
      isHighlighted.addAll([false, true, false]); // Only middle highlighted
    } else {
      // Normal case: previous, current, next
      displayLyrics.add(lyrics[currentLyricIndex - 1]); // Previous
      displayLyrics.add(lyrics[currentLyricIndex]); // Current in middle
      displayLyrics.add(lyrics[currentLyricIndex + 1]); // Next
      isHighlighted.addAll([false, true, false]); // Only middle highlighted
    }

    // Fallback content if still no lyrics (shouldn't happen now)
    if (displayLyrics.isEmpty) {
      displayLyrics.addAll([
        'THE CLUB ISN\'T THE BEST',
        'PLACE TO FIND A LOVER',
        'SO THE BAR IS WHERE I GO'
      ]);
      isHighlighted.addAll([false, true, false]); // Highlight middle line
    }

    // Pre-calculate all text painters to determine actual heights
    List<TextPainter> painters = [];
    List<double> textHeights = [];

    for (int i = 0; i < displayLyrics.length; i++) {
      final isCurrentLine = isHighlighted.length > i ? isHighlighted[i] : false;
      final lyricText = displayLyrics[i];

      // Skip rendering empty lines but maintain spacing
      if (lyricText.isEmpty) {
        painters.add(TextPainter(
            text: const TextSpan(text: ''), textDirection: TextDirection.ltr));
        textHeights.add(0); // No height for empty lines
        continue;
      }

      // Use template font size, make current line larger
      double fontSize = isCurrentLine
          ? (element.style.fontSize ?? 64)
          : (element.style.fontSize ?? 64) *
              0.7; // Non-current lines are 70% size

      // Colors for highlighted vs non-highlighted - proper contrast
      Color textColor = isCurrentLine
          ? const Color(0xFFE955BB) // Exact pink-purple base color as specified
          : const Color(
              0xFF888888); // Better visibility for previous/next lines

      final painter = TextPainter(
        text: TextSpan(
          text: lyricText.toUpperCase(),
          style: TextStyle(
            fontFamily: 'Ethnocentric',
            color: textColor,
            fontSize: fontSize,
            fontWeight: isCurrentLine
                ? FontWeight.w900
                : FontWeight.w600, // Bolder fonts
            letterSpacing: isCurrentLine ? 1.5 : 1.0, // More condensed spacing
            shadows: isCurrentLine
                ? [
                    // Proper neon pink-purple glow for current line
                    Shadow(
                      color: Colors.black.withValues(alpha: 0.9),
                      offset: const Offset(3, 3),
                      blurRadius: 6,
                    ),
                    Shadow(
                      color: const Color(0xFFE955BB).withValues(alpha: 0.8),
                      offset: const Offset(0, 0),
                      blurRadius: 15,
                    ),
                    Shadow(
                      color: const Color(0xFFE955BB).withValues(alpha: 0.4),
                      offset: const Offset(0, 0),
                      blurRadius: 30, // Outer glow
                    ),
                  ]
                : [
                    // Subtle glow for previous/next lines to make them visible
                    Shadow(
                      color: Colors.black.withValues(alpha: 0.6),
                      offset: const Offset(2, 2),
                      blurRadius: 4,
                    ),
                  ],
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
        maxLines: null, // Allow multiple lines for wrapping
      );

      // Layout with proper width constraint - narrower width for better look
      painter.layout(
          maxWidth: position.width -
              300); // Reduced from 40 to 300 for narrower lyrics

      painters.add(painter);
      textHeights.add(painter.height);
    }

    // Calculate total height needed and dynamic spacing
    double totalTextHeight = textHeights.fold(0, (sum, height) => sum + height);
    double availableHeight =
        position.height - 120; // Leave top/bottom margin (increased top margin)
    double dynamicSpacing =
        (availableHeight - totalTextHeight) / (displayLyrics.length + 1);

    // Ensure minimum spacing to prevent overlap - reduced for tighter spacing
    dynamicSpacing = math.max(dynamicSpacing, 20); // Reduced from 30 to 20

    // Start Y position (shifted down to create more space from waves video)
    double currentY = position.y +
        150; // Increased top margin from 80 to 150 for more spacing

    // Render each lyric with dynamic spacing
    for (int i = 0; i < painters.length; i++) {
      final painter = painters[i];

      // Skip rendering empty lines but maintain spacing
      if (displayLyrics[i].isEmpty) {
        currentY += dynamicSpacing; // Just add spacing for empty lines
        continue;
      }

      // Center each lyric block horizontally
      final x = position.x + (position.width - painter.width) / 2;

      painter.paint(canvas, Offset(x, currentY));

      // Move to next position: current text height + dynamic spacing
      currentY += textHeights[i] + dynamicSpacing;
    }
  }

  static Future<void> _renderDecoration(
      Canvas canvas, CanvaElement element, int frameIndex) async {
    // Render animated decorative elements like particles
    final style = element.style;

    if (element.content == 'particles') {
      final particlePaint = Paint()
        ..color = (style.color ?? Colors.white)
            .withValues(alpha: style.opacity ?? 0.1)
        ..style = PaintingStyle.fill;

      for (int i = 0; i < 20; i++) {
        final x = (i * 54.0 + frameIndex * 2) % 1080;
        final y = (i * 96.0 + frameIndex * 1.5) % 1920;
        canvas.drawCircle(Offset(x, y), 2, particlePaint);
      }
    }
  }
}
