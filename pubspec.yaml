name: melodyze_demo
description: A demo Flutter app for scrolling lyrics with audio.
publish_to: 'none'
version: 1.0.0+1

flutter_version: "3.29.1"

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  audioplayers: ^6.5.0
  scrollable_positioned_list: ^0.3.8
  image_picker: ^1.0.4
  shared_preferences: ^2.2.2
  file_picker:  ^8.1.2
  permission_handler: ^11.0.1

  camera: ^0.10.5+9
  video_player: ^2.8.1
  ffmpeg_kit_flutter_new: ^3.1.0
  media_store_plus: ^0.1.3
  path_provider: ^2.1.1
  share_plus: ^7.2.1
  device_info_plus: ^10.0.0

  

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/dummy_audio.mp3
    - assets/dummy_dp.jpg
    - assets/lyrics.json
    - assets/canva_templates.json
    - assets/MicrosoftTeams-video.mp4
    - assets/profile border.png
    - assets/logo_melodyze.png
  fonts:
    - family: Ethnocentric
      fonts:
        - asset: assets/fonts/Ethnocentric Rg.otf
          weight: 400
        - asset: assets/fonts/Ethnocentric Rg It.otf
          weight: 400
          style: italic